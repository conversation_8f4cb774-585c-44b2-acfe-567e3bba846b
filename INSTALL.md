# Installation Guide

This guide provides detailed instructions for installing the Species-Specific Primer Design Pipeline and its dependencies.

## System Requirements

- **Operating System**: Linux or macOS (Windows with WSL)
- **Python**: 3.8 or higher
- **Memory**: At least 8GB RAM (16GB recommended for large datasets)
- **Storage**: At least 10GB free space for genome downloads and analysis

## Dependencies

### External Tools

The pipeline requires several external bioinformatics tools:

1. **NCBI Datasets CLI** - For genome downloading
2. **skani** - For fast ANI calculations (recommended)
3. **FastANI** - Alternative ANI calculation tool
4. **Jellyfish** - For k-mer counting

### Installation Steps

#### 1. Install Conda/Mamba (Recommended)

```bash
# Install Miniconda
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh

# Or install Mamba (faster)
wget https://github.com/conda-forge/miniforge/releases/latest/download/Mambaforge-Linux-x86_64.sh
bash Mambaforge-Linux-x86_64.sh
```

#### 2. Create Conda Environment

```bash
# Create environment with required tools
conda create -n primer_pipeline python=3.9
conda activate primer_pipeline

# Install bioinformatics tools
conda install -c conda-forge ncbi-datasets-cli
conda install -c bioconda skani
conda install -c bioconda fastani
conda install -c bioconda jellyfish
```

#### 3. Install Python Dependencies

```bash
# Clone the repository
git clone <repository-url>
cd ss_primer_design

# Install Python packages
pip install -r requirements.txt

# Or install in development mode
pip install -e .
```

#### 4. Verify Installation

```bash
# Test the pipeline components
python test_pipeline.py

# Check external tools
datasets --help
skani --help
fastANI --help
jellyfish --help
```

## Alternative Installation Methods

### Using Docker (Recommended for Production)

```bash
# Build Docker image
docker build -t primer-pipeline .

# Run pipeline in container
docker run -v $(pwd)/data:/data primer-pipeline \
  --species "Escherichia coli" --output-dir /data/output
```

### Manual Installation

If you prefer to install dependencies manually:

#### NCBI Datasets CLI

```bash
# Download and install
curl -o datasets 'https://ftp.ncbi.nlm.nih.gov/pub/datasets/command-line/v2/linux-amd64/datasets'
curl -o dataformat 'https://ftp.ncbi.nlm.nih.gov/pub/datasets/command-line/v2/linux-amd64/dataformat'
chmod +x datasets dataformat
sudo mv datasets dataformat /usr/local/bin/
```

#### skani (Recommended)

```bash
# From pre-compiled binary (fastest)
wget https://github.com/bluenote-1577/skani/releases/latest/download/skani-v0.2.1-x86_64-unknown-linux-musl.tar.gz
tar xzf skani-v0.2.1-x86_64-unknown-linux-musl.tar.gz
sudo cp skani-v0.2.1-x86_64-unknown-linux-musl/skani /usr/local/bin/

# Or from source (requires Rust)
git clone https://github.com/bluenote-1577/skani
cd skani
cargo build --release
sudo cp target/release/skani /usr/local/bin/
```

#### FastANI (Alternative)

```bash
# From source
git clone https://github.com/ParBLiSS/FastANI.git
cd FastANI
make -j
sudo cp fastANI /usr/local/bin/
```

#### Jellyfish

```bash
# From source
wget https://github.com/gmarcais/Jellyfish/releases/download/v2.3.0/jellyfish-2.3.0.tar.gz
tar xzf jellyfish-2.3.0.tar.gz
cd jellyfish-2.3.0
./configure --prefix=/usr/local
make -j
sudo make install
```

## Configuration

### NCBI API Key (Optional but Recommended)

To avoid rate limiting when downloading genomes:

1. Register for an NCBI account
2. Generate an API key
3. Add to configuration:

```yaml
# config/custom_config.yaml
genome_download:
  ncbi_api_key: "your_api_key_here"
```

### Custom Configuration

Copy and modify the default configuration:

```bash
cp config/default_config.yaml config/my_config.yaml
# Edit config/my_config.yaml as needed
```

## Troubleshooting

### Common Issues

1. **Permission Denied for External Tools**
   ```bash
   chmod +x /path/to/tool
   ```

2. **Python Module Not Found**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **Memory Issues with Large Datasets**
   - Reduce `max_target_genomes` in configuration
   - Use smaller k-mer hash size in Jellyfish settings

4. **Network Issues with Genome Downloads**
   - Check internet connection
   - Verify NCBI Datasets CLI installation
   - Consider using NCBI API key

### Getting Help

- Check the [FAQ](FAQ.md)
- Review the [troubleshooting guide](TROUBLESHOOTING.md)
- Open an issue on GitHub

## Testing the Installation

Run the test suite to verify everything is working:

```bash
# Basic functionality test
python test_pipeline.py

# Test with small dataset (if available)
python primer_pipeline.py --species "Escherichia coli" --config config/test_config.yaml
```

## Next Steps

After successful installation:

1. Read the [User Guide](USER_GUIDE.md)
2. Review the [Configuration Reference](CONFIG.md)
3. Try the [Tutorial](TUTORIAL.md)
4. Check out [Examples](examples/)
