#!/usr/bin/env python3
"""
Species-Specific Primer Design Pipeline

Command-line interface for the modular primer design pipeline.
"""

import click
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from primer_pipeline import PrimerPipeline


@click.command()
@click.option('--species', required=True, help='Species Latin name (e.g., "Escherichia coli")')
@click.option('--start-stage', type=int, default=1, help='Stage number to start from (1-7)')
@click.option('--config', type=click.Path(exists=True), help='Path to custom configuration file')
@click.option('--output-dir', type=click.Path(), help='Output directory path')
@click.option('--resume', is_flag=True, help='Auto-resume from last completed stage')
@click.option('--force-rerun', is_flag=True, help='Force rerun of all stages')
@click.option('--list-stages', is_flag=True, help='List all pipeline stages and exit')
@click.option('--stage-info', type=int, help='Show information about a specific stage')
def main(species, start_stage, config, output_dir, resume, force_rerun, list_stages, stage_info):
    """
    Species-Specific Primer Design Pipeline
    
    Design PCR primers specific to a microbial species using a modular pipeline
    that includes genome download, quality assessment, taxonomic validation,
    k-mer analysis, and primer design.
    
    Examples:
    
        # Basic usage
        python primer_pipeline.py --species "Escherichia coli"
        
        # Resume from stage 3
        python primer_pipeline.py --species "Escherichia coli" --start-stage 3
        
        # Auto-resume from last checkpoint
        python primer_pipeline.py --species "Escherichia coli" --resume
        
        # Use custom configuration
        python primer_pipeline.py --species "Escherichia coli" --config my_config.yaml
    """
    
    try:
        # Initialize pipeline
        pipeline = PrimerPipeline(
            species_name=species,
            config_file=config,
            output_dir=output_dir,
            start_stage=start_stage,
            resume=resume
        )
        
        # Handle special commands
        if list_stages:
            click.echo("Pipeline Stages:")
            for stage_num, stage_name in pipeline.list_stages().items():
                status = "✓" if pipeline.checkpoint_manager.is_stage_completed(stage_num) else "○"
                click.echo(f"  {status} Stage {stage_num}: {stage_name}")
            return
        
        if stage_info:
            info = pipeline.get_stage_info(stage_info)
            if info:
                click.echo(f"Stage {info['stage_number']}: {info['stage_name']}")
                click.echo(f"Completed: {info['completed']}")
                if info['checkpoint_info']:
                    click.echo(f"Last run: {info['checkpoint_info'].timestamp}")
                    click.echo(f"Status: {info['checkpoint_info'].status}")
            else:
                click.echo(f"Stage {stage_info} not found")
            return
        
        # Validate stage number
        if start_stage < 1 or start_stage > 7:
            click.echo("Error: Stage number must be between 1 and 7", err=True)
            sys.exit(1)
        
        # Run pipeline
        click.echo(f"Starting pipeline for species: {species}")
        if resume:
            click.echo("Resume mode enabled - will start from last completed stage")
        elif start_stage > 1:
            click.echo(f"Starting from stage {start_stage}")
        
        results = pipeline.run(force_rerun=force_rerun)
        
        # Display summary
        click.echo("\n" + "="*60)
        click.echo("PIPELINE COMPLETED SUCCESSFULLY")
        click.echo("="*60)
        
        summary = results.get('summary', {})
        if 'primer_pairs' in results and results['primer_pairs']:
            click.echo(f"Primer pairs designed: {len(results['primer_pairs'])}")
            
            # Show top primer pair
            top_primer = results['primer_pairs'][0]
            click.echo(f"\nTop primer pair:")
            click.echo(f"  Forward: {top_primer.get('forward_seq', 'N/A')}")
            click.echo(f"  Reverse: {top_primer.get('reverse_seq', 'N/A')}")
            click.echo(f"  Product size: {top_primer.get('product_size', 'N/A')} bp")
            click.echo(f"  Specificity score: {top_primer.get('specificity_score', 'N/A')}")
        
        click.echo(f"\nOutput directory: {pipeline.output_dirs['base']}")
        click.echo(f"Full report: {pipeline.output_dirs['base']}/pipeline_report.json")
        
    except KeyboardInterrupt:
        click.echo("\nPipeline interrupted by user", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Pipeline failed: {str(e)}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    main()
