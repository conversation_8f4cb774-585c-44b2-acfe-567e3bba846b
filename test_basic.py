#!/usr/bin/env python3
"""
Basic test script for the Species-Specific Primer Design Pipeline

This script tests basic functionality without requiring all external dependencies.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


def test_config_loading():
    """Test configuration loading."""
    print("Testing configuration loading...")
    
    try:
        from primer_pipeline.config import Config
        config = Config.load()
        print(f"✓ Configuration loaded successfully")
        print(f"  - Max target genomes: {config.get('general.max_target_genomes')}")
        print(f"  - K-mer size: {config.get('kmer_analysis.kmer_size')}")
        print(f"  - ANI threshold: {config.get('ani_analysis.same_species_threshold')}")
        return True
    except Exception as e:
        print(f"✗ Configuration loading failed: {e}")
        return False


def test_checkpoint_manager():
    """Test checkpoint manager functionality."""
    print("\nTesting checkpoint manager...")
    
    try:
        from primer_pipeline.checkpoint import CheckpointManager
        
        checkpoint_manager = CheckpointManager("test_checkpoints")
        
        # Test saving a checkpoint
        checkpoint_manager.save_checkpoint(
            stage_number=1,
            stage_name="Test Stage",
            input_params={"test": "value"},
            output_files=["test_file.txt"],
            metadata={"test_meta": "data"}
        )
        
        # Test loading checkpoint info
        info = checkpoint_manager.get_checkpoint_info(1)
        if info and info.stage_name == "Test Stage":
            print("✓ Checkpoint manager working correctly")
            return True
        else:
            print("✗ Checkpoint manager test failed")
            return False
            
    except Exception as e:
        print(f"✗ Checkpoint manager test failed: {e}")
        return False


def test_logger():
    """Test logging functionality."""
    print("\nTesting logger...")
    
    try:
        from primer_pipeline.utils.logger import setup_logger
        logger = setup_logger(level="INFO")
        logger.info("Test info message")
        logger.warning("Test warning message")
        logger.stage_start("Test Stage", 1)
        logger.stage_complete("Test Stage", 1)
        print("✓ Logger working correctly")
        return True
    except Exception as e:
        print(f"✗ Logger test failed: {e}")
        return False


def test_base_stage():
    """Test base stage functionality."""
    print("\nTesting base stage...")
    
    try:
        from primer_pipeline.stages.base_stage import BaseStage
        from primer_pipeline.config import Config
        from primer_pipeline.checkpoint import CheckpointManager
        from primer_pipeline.utils.logger import setup_logger
        
        config = Config.load()
        checkpoint_manager = CheckpointManager("test_checkpoints")
        logger = setup_logger(level="INFO")
        
        # Create a dummy stage class
        class TestStage(BaseStage):
            def run(self, input_data=None):
                return {"test": "output"}
            
            def validate_input(self, input_data):
                return True
            
            def get_output_files(self):
                return ["test_output.txt"]
        
        stage = TestStage(config, checkpoint_manager, logger, 1, "Test Stage")
        print("✓ Base stage created successfully")
        return True
        
    except Exception as e:
        print(f"✗ Base stage test failed: {e}")
        return False


def test_cli_help():
    """Test CLI help functionality."""
    print("\nTesting CLI help...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "primer_pipeline.py", "--help"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "Species-Specific Primer Design Pipeline" in result.stdout:
            print("✓ CLI help working correctly")
            return True
        else:
            print(f"✗ CLI help failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ CLI help test failed: {e}")
        return False


def main():
    """Run basic tests."""
    print("Species-Specific Primer Design Pipeline - Basic Test Suite")
    print("=" * 60)
    
    tests = [
        test_config_loading,
        test_checkpoint_manager,
        test_logger,
        test_base_stage,
        test_cli_help
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Basic Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ Basic functionality working! Install dependencies to run full pipeline.")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Install external tools (see INSTALL.md)")
        print("3. Run pipeline: python primer_pipeline.py --species 'Escherichia coli'")
    else:
        print("✗ Some basic tests failed. Check the code and configuration.")
        
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
