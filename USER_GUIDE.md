# User Guide

This guide provides comprehensive instructions for using the Species-Specific Primer Design Pipeline.

## Quick Start

### Basic Usage

```bash
# Design primers for E. coli
python primer_pipeline.py --species "Escherichia coli"

# Specify output directory
python primer_pipeline.py --species "Salmonella enterica" --output-dir results/salmonella

# Use custom configuration
python primer_pipeline.py --species "Bacillus subtilis" --config my_config.yaml
```

### Pipeline Stages

The pipeline consists of 7 stages that run sequentially:

1. **Genome Download & Selection** - Downloads genomes from NCBI
2. **Related Species Collection** - Collects genomes from related species
3. **Quality Assessment & Filtering** - Filters genomes by quality metrics
4. **Taxonomic Validation via ANI** - Validates classifications using ANI
5. **Species-Specific K-mer Analysis** - Identifies species-specific k-mers
6. **Genomic Region Identification** - Maps k-mers to genomic regions
7. **Primer Design** - Designs and validates PCR primers

## Command Line Options

### Required Arguments

- `--species "Species name"` - Target species Latin name (e.g., "Escherichia coli")

### Optional Arguments

- `--start-stage N` - Start from stage N (1-7)
- `--config FILE` - Path to custom configuration file
- `--output-dir DIR` - Output directory path
- `--resume` - Auto-resume from last completed stage
- `--force-rerun` - Force rerun of all stages
- `--list-stages` - List all pipeline stages and exit
- `--stage-info N` - Show information about stage N

## Configuration

### Default Configuration

The pipeline uses `config/default_config.yaml` by default. Key parameters:

```yaml
general:
  max_target_genomes: 100
  max_related_genomes_per_species: 50
  log_level: "INFO"

ani_analysis:
  ani_method: "skani"  # skani (fast), fastani (standard), pyani (slow)
  skani_preset: "medium"  # fast, medium, slow

kmer_analysis:
  kmer_size: 21
  min_kmer_frequency: 5
  specificity_threshold: 0.95

primer_design:
  primer_opt_size: 20
  primer_opt_tm: 60.0
  product_size_range: [100, 300]
```

### Custom Configuration

Create a custom configuration file:

```yaml
# my_config.yaml
general:
  max_target_genomes: 50  # Reduce for faster processing
  
kmer_analysis:
  kmer_size: 19  # Smaller k-mers for more sensitivity
  
primer_design:
  product_size_range: [150, 250]  # Narrower product size range
```

## Resumability and Checkpoints

### Automatic Resume

```bash
# Pipeline will automatically resume from last completed stage
python primer_pipeline.py --species "Escherichia coli" --resume
```

### Manual Stage Selection

```bash
# Start from stage 3 (Quality Assessment)
python primer_pipeline.py --species "Escherichia coli" --start-stage 3

# Check stage status
python primer_pipeline.py --species "Escherichia coli" --list-stages
```

### Stage Information

```bash
# Get detailed information about stage 4
python primer_pipeline.py --species "Escherichia coli" --stage-info 4
```

## Output Structure

The pipeline creates a structured output directory:

```
output/
├── stage_01_genome_download_selection/
│   ├── target_genomes/
│   ├── genome_metadata.csv
│   └── download_summary.json
├── stage_02_related_species_collection/
│   ├── related_genomes/
│   └── related_species_metadata.csv
├── stage_03_quality_assessment_filtering/
│   ├── genome_metrics.csv
│   ├── filtered_genomes.csv
│   └── quality_plots.png
├── stage_04_taxonomic_validation_via_ani/
│   ├── ani_matrix.csv
│   └── ani_analysis_plots.png
├── stage_05_species_specific_k_mer_analysis/
│   ├── species_specific_kmers.txt
│   └── kmer_analysis_plots.png
├── stage_06_genomic_region_identification/
│   ├── candidate_regions.fasta
│   └── candidate_regions.csv
├── stage_07_primer_design/
│   ├── primer_pairs.csv
│   └── primer_analysis_plots.png
├── checkpoints/
├── logs/
└── pipeline_report.json
```

## Understanding Results

### Final Primer Pairs

The main output is `stage_07_primer_design/primer_pairs.csv`:

```csv
rank,region_id,forward_seq,reverse_seq,forward_tm,reverse_tm,product_size,specificity_score
1,region_contig1_1000,ATGCGATCGATCGATCG,CGATCGATCGATCGCAT,59.5,60.2,180,1.0
```

### Key Metrics

- **Specificity Score**: 1.0 = perfect specificity, 0.0 = no specificity
- **Target Hits**: Number of matches in target species genomes
- **Related Hits**: Number of matches in related species genomes
- **Tm**: Melting temperature (optimal: 57-63°C)
- **Product Size**: PCR amplicon size

### Quality Indicators

Good primer pairs should have:
- Specificity score > 0.95
- Related hits = 0
- Tm difference < 3°C between forward and reverse
- GC content 40-60%
- Product size within specified range

## Advanced Usage

### Large Datasets

For species with many genomes:

```yaml
# config/large_dataset.yaml
general:
  max_target_genomes: 200
  max_related_genomes_per_species: 20

quality_assessment:
  min_n50: 50000  # Stricter quality filters
```

### High Sensitivity

For closely related species:

```yaml
# config/high_sensitivity.yaml
kmer_analysis:
  kmer_size: 25  # Longer k-mers for specificity
  specificity_threshold: 0.99

ani_analysis:
  same_species_threshold: 97.0  # Stricter ANI threshold
```

### Fast Processing

For quick results:

```yaml
# config/fast.yaml
general:
  max_target_genomes: 20
  max_related_genomes_per_species: 5

ani_analysis:
  ani_method: "skani"  # Fastest ANI method
  skani_preset: "fast"  # Use fast preset

region_identification:
  max_regions_per_genome: 20

primer_design:
  max_primer_pairs: 5
```

### ANI Method Selection

Choose the appropriate ANI calculation method:

```yaml
# For speed (recommended for most cases)
ani_analysis:
  ani_method: "skani"
  skani_preset: "fast"    # ~10x faster than FastANI
  skani_min_af: 0.15      # Minimum alignment fraction

# For standard analysis
ani_analysis:
  ani_method: "skani"
  skani_preset: "medium"  # Good balance of speed and accuracy

# For high accuracy (slower)
ani_analysis:
  ani_method: "fastani"   # Traditional method
  fragment_length: 3000
  min_fraction: 0.2
```

## Troubleshooting

### Common Issues

1. **No genomes found**
   - Check species name spelling
   - Try broader search terms
   - Check NCBI connectivity

2. **Low specificity primers**
   - Increase k-mer size
   - Raise specificity threshold
   - Include more related species

3. **No primer pairs generated**
   - Lower quality thresholds
   - Increase product size range
   - Check region identification parameters

4. **Memory issues**
   - Reduce max_target_genomes
   - Use smaller jellyfish hash size
   - Process in smaller batches

### Performance Optimization

- Use SSD storage for faster I/O
- Increase thread count in configuration
- Use NCBI API key for faster downloads
- Consider using cluster computing for large datasets

## Best Practices

1. **Species Selection**: Use complete, well-annotated species names
2. **Configuration**: Start with defaults, then customize based on results
3. **Quality Control**: Review quality plots and metrics at each stage
4. **Validation**: Test primers experimentally before large-scale use
5. **Documentation**: Keep track of parameters used for reproducibility

## Getting Help

- Check log files in the `logs/` directory
- Review stage reports (JSON files) for detailed information
- Use `--list-stages` and `--stage-info` for pipeline status
- Consult the troubleshooting guide for common issues
