#!/usr/bin/env python3
"""
Test script for the Species-Specific Primer Design Pipeline

This script demonstrates basic usage and tests the pipeline components.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from primer_pipeline import Primer<PERSON><PERSON>eline, Config
from primer_pipeline.utils.logger import setup_logger


def test_config_loading():
    """Test configuration loading."""
    print("Testing configuration loading...")
    
    try:
        config = Config.load()
        print(f"✓ Configuration loaded successfully")
        print(f"  - Max target genomes: {config.get('general.max_target_genomes')}")
        print(f"  - K-mer size: {config.get('kmer_analysis.kmer_size')}")
        print(f"  - ANI threshold: {config.get('ani_analysis.same_species_threshold')}")
        return True
    except Exception as e:
        print(f"✗ Configuration loading failed: {e}")
        return False


def test_pipeline_initialization():
    """Test pipeline initialization."""
    print("\nTesting pipeline initialization...")
    
    try:
        pipeline = PrimerPipeline(
            species_name="Escherichia coli",
            output_dir="test_output"
        )
        print(f"✓ Pipeline initialized successfully")
        print(f"  - Species: {pipeline.species_name}")
        print(f"  - Output directory: {pipeline.output_dirs['base']}")
        print(f"  - Number of stages: {len(pipeline.stages)}")
        return True
    except Exception as e:
        print(f"✗ Pipeline initialization failed: {e}")
        return False


def test_stage_listing():
    """Test stage listing functionality."""
    print("\nTesting stage listing...")
    
    try:
        pipeline = PrimerPipeline(
            species_name="Escherichia coli",
            output_dir="test_output"
        )
        
        stages = pipeline.list_stages()
        print(f"✓ Stage listing successful")
        for stage_num, stage_name in stages.items():
            print(f"  - Stage {stage_num}: {stage_name}")
        return True
    except Exception as e:
        print(f"✗ Stage listing failed: {e}")
        return False


def test_checkpoint_manager():
    """Test checkpoint manager functionality."""
    print("\nTesting checkpoint manager...")
    
    try:
        from primer_pipeline.checkpoint import CheckpointManager
        
        checkpoint_manager = CheckpointManager("test_checkpoints")
        
        # Test saving a checkpoint
        checkpoint_manager.save_checkpoint(
            stage_number=1,
            stage_name="Test Stage",
            input_params={"test": "value"},
            output_files=["test_file.txt"],
            metadata={"test_meta": "data"}
        )
        
        # Test loading checkpoint info
        info = checkpoint_manager.get_checkpoint_info(1)
        if info and info.stage_name == "Test Stage":
            print("✓ Checkpoint manager working correctly")
            return True
        else:
            print("✗ Checkpoint manager test failed")
            return False
            
    except Exception as e:
        print(f"✗ Checkpoint manager test failed: {e}")
        return False


def test_logger():
    """Test logging functionality."""
    print("\nTesting logger...")
    
    try:
        logger = setup_logger(level="INFO")
        logger.info("Test info message")
        logger.warning("Test warning message")
        logger.stage_start("Test Stage", 1)
        logger.stage_complete("Test Stage", 1)
        print("✓ Logger working correctly")
        return True
    except Exception as e:
        print(f"✗ Logger test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("Species-Specific Primer Design Pipeline - Test Suite")
    print("=" * 60)
    
    tests = [
        test_config_loading,
        test_pipeline_initialization,
        test_stage_listing,
        test_checkpoint_manager,
        test_logger
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Pipeline is ready to use.")
        print("\nTo run the pipeline:")
        print('python primer_pipeline.py --species "Escherichia coli"')
    else:
        print("✗ Some tests failed. Please check the configuration and dependencies.")
        sys.exit(1)


if __name__ == "__main__":
    main()
