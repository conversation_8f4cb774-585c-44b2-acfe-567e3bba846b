#!/usr/bin/env python3
"""
Setup script for the Species-Specific Primer Design Pipeline
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text() if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

setup(
    name="ss-primer-design",
    version="1.0.0",
    description="A modular microbial species-specific primer design pipeline",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Primer Pipeline Team",
    author_email="<EMAIL>",
    url="https://github.com/your-username/ss_primer_design",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    include_package_data=True,
    install_requires=requirements,
    python_requires=">=3.8",
    entry_points={
        "console_scripts": [
            "primer-pipeline=primer_pipeline.cli:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "Topic :: Scientific/Engineering :: Bio-Informatics",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    keywords="bioinformatics primers pcr microbiology genomics",
    project_urls={
        "Bug Reports": "https://github.com/your-username/ss_primer_design/issues",
        "Source": "https://github.com/your-username/ss_primer_design",
        "Documentation": "https://github.com/your-username/ss_primer_design/wiki",
    },
)
