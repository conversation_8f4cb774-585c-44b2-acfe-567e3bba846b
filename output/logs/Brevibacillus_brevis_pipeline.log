2025-07-13 17:15:10 - primer_pipeline - INFO - Starting Species-Specific Primer Design Pipeline
2025-07-13 17:15:10 - primer_pipeline - INFO - Target species: Brevibacillus brevis
2025-07-13 17:15:10 - primer_pipeline - INFO - Starting from stage: 1
2025-07-13 17:15:10 - primer_pipeline - INFO - Output directory: output
2025-07-13 17:15:10 - primer_pipeline - INFO - ============================================================
2025-07-13 17:15:10 - primer_pipeline - INFO - STAGE 1: Genome Download & Selection
2025-07-13 17:15:10 - primer_pipeline - INFO - Started at: 2025-07-13 17:15:10
2025-07-13 17:15:10 - primer_pipeline - INFO - ============================================================
2025-07-13 17:15:10 - primer_pipeline - INFO - Downloading genomes for species: Brevibacillus brevis
2025-07-13 17:15:10 - primer_pipeline - INFO - Searching NCBI for available genomes...
2025-07-13 17:15:10 - primer_pipeline - ERROR - Failed to search genomes: Error: unknown flag: --refseq-category

Use datasets summary genome taxon <command> --help for detailed help about a command.


2025-07-13 17:15:10 - primer_pipeline - ERROR - Stage 1 (Genome Download & Selection) failed: Command 'datasets summary genome taxon "Brevibacillus brevis" --as-json-lines --assembly-level Complete Genome,Chromosome,Scaffold,Contig --refseq-category reference genome,representative genome --exclude-partial --exclude-anomalous' returned non-zero exit status 1.
2025-07-13 17:15:10 - primer_pipeline - ERROR - Pipeline failed: Command 'datasets summary genome taxon "Brevibacillus brevis" --as-json-lines --assembly-level Complete Genome,Chromosome,Scaffold,Contig --refseq-category reference genome,representative genome --exclude-partial --exclude-anomalous' returned non-zero exit status 1.
