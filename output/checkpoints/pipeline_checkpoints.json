{"1": {"stage_number": 1, "stage_name": "Genome Download & Selection", "timestamp": "2025-07-13T17:15:10.129261", "status": "failed", "input_params": {"species_name": "Brevibacillus brevis"}, "output_files": [], "metadata": {"error": "Command 'datasets summary genome taxon \"Brevibacillus brevis\" --as-json-lines --assembly-level Complete Genome,Chromosome,Scaffold,Contig --refseq-category reference genome,representative genome --exclude-partial --exclude-anomalous' returned non-zero exit status 1."}}}