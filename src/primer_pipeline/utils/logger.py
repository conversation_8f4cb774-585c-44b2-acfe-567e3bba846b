"""
Logging utilities for the primer design pipeline.
"""

import logging
import sys
from pathlib import Path
from typing import Optional
from datetime import datetime


class PipelineLogger:
    """Custom logger for the primer design pipeline."""
    
    def __init__(self, name: str = "primer_pipeline", level: str = "INFO", 
                 log_file: Optional[str] = None):
        """Initialize logger."""
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler (if specified)
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def debug(self, message: str) -> None:
        """Log debug message."""
        self.logger.debug(message)
    
    def info(self, message: str) -> None:
        """Log info message."""
        self.logger.info(message)
    
    def warning(self, message: str) -> None:
        """Log warning message."""
        self.logger.warning(message)
    
    def error(self, message: str) -> None:
        """Log error message."""
        self.logger.error(message)
    
    def critical(self, message: str) -> None:
        """Log critical message."""
        self.logger.critical(message)
    
    def stage_start(self, stage_name: str, stage_number: int) -> None:
        """Log stage start."""
        self.info(f"{'='*60}")
        self.info(f"STAGE {stage_number}: {stage_name}")
        self.info(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.info(f"{'='*60}")
    
    def stage_complete(self, stage_name: str, stage_number: int) -> None:
        """Log stage completion."""
        self.info(f"STAGE {stage_number}: {stage_name} - COMPLETED")
        self.info(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.info(f"{'='*60}")
    
    def progress(self, current: int, total: int, description: str = "") -> None:
        """Log progress information."""
        percentage = (current / total) * 100 if total > 0 else 0
        self.info(f"Progress: {current}/{total} ({percentage:.1f}%) {description}")


def setup_logger(name: str = "primer_pipeline", level: str = "INFO", 
                log_file: Optional[str] = None) -> PipelineLogger:
    """Setup and return a pipeline logger."""
    return PipelineLogger(name=name, level=level, log_file=log_file)
