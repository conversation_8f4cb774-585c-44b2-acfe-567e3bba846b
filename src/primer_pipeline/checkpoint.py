"""
Checkpoint management for pipeline resumability.
"""

import json
import pickle
import gzip
from pathlib import Path
from typing import Any, Dict, List, Optional
from datetime import datetime
from dataclasses import dataclass, asdict


@dataclass
class CheckpointInfo:
    """Information about a pipeline checkpoint."""
    stage_number: int
    stage_name: str
    timestamp: str
    status: str  # 'completed', 'failed', 'in_progress'
    input_params: Dict[str, Any]
    output_files: List[str]
    metadata: Dict[str, Any]


class CheckpointManager:
    """Manages pipeline checkpoints for resumability."""
    
    def __init__(self, checkpoint_dir: str, compress: bool = True):
        """Initialize checkpoint manager."""
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        self.compress = compress
        self.checkpoint_file = self.checkpoint_dir / "pipeline_checkpoints.json"
        self.checkpoints: Dict[int, CheckpointInfo] = {}
        
        # Load existing checkpoints
        self._load_checkpoints()
    
    def _load_checkpoints(self) -> None:
        """Load existing checkpoints from file."""
        if self.checkpoint_file.exists():
            try:
                with open(self.checkpoint_file, 'r') as f:
                    data = json.load(f)
                
                for stage_num, checkpoint_data in data.items():
                    self.checkpoints[int(stage_num)] = CheckpointInfo(**checkpoint_data)
            except Exception as e:
                print(f"Warning: Could not load checkpoints: {e}")
    
    def _save_checkpoints(self) -> None:
        """Save checkpoints to file."""
        data = {}
        for stage_num, checkpoint in self.checkpoints.items():
            data[stage_num] = asdict(checkpoint)
        
        with open(self.checkpoint_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def save_checkpoint(self, stage_number: int, stage_name: str, 
                       input_params: Dict[str, Any], output_files: List[str],
                       metadata: Dict[str, Any] = None, status: str = "completed") -> None:
        """Save a checkpoint for a completed stage."""
        if metadata is None:
            metadata = {}
        
        checkpoint = CheckpointInfo(
            stage_number=stage_number,
            stage_name=stage_name,
            timestamp=datetime.now().isoformat(),
            status=status,
            input_params=input_params,
            output_files=output_files,
            metadata=metadata
        )
        
        self.checkpoints[stage_number] = checkpoint
        self._save_checkpoints()
    
    def save_stage_data(self, stage_number: int, data: Any, 
                       filename: Optional[str] = None) -> str:
        """Save stage data to checkpoint directory."""
        if filename is None:
            filename = f"stage_{stage_number}_data"
        
        if self.compress:
            filepath = self.checkpoint_dir / f"{filename}.pkl.gz"
            with gzip.open(filepath, 'wb') as f:
                pickle.dump(data, f)
        else:
            filepath = self.checkpoint_dir / f"{filename}.pkl"
            with open(filepath, 'wb') as f:
                pickle.dump(data, f)
        
        return str(filepath)
    
    def load_stage_data(self, stage_number: int, filename: Optional[str] = None) -> Any:
        """Load stage data from checkpoint directory."""
        if filename is None:
            filename = f"stage_{stage_number}_data"
        
        # Try compressed first
        filepath_gz = self.checkpoint_dir / f"{filename}.pkl.gz"
        filepath_pkl = self.checkpoint_dir / f"{filename}.pkl"
        
        if filepath_gz.exists():
            with gzip.open(filepath_gz, 'rb') as f:
                return pickle.load(f)
        elif filepath_pkl.exists():
            with open(filepath_pkl, 'rb') as f:
                return pickle.load(f)
        else:
            raise FileNotFoundError(f"Checkpoint data not found: {filename}")
    
    def get_last_completed_stage(self) -> Optional[int]:
        """Get the number of the last completed stage."""
        completed_stages = [
            stage_num for stage_num, checkpoint in self.checkpoints.items()
            if checkpoint.status == "completed"
        ]
        return max(completed_stages) if completed_stages else None
    
    def is_stage_completed(self, stage_number: int) -> bool:
        """Check if a stage is completed."""
        return (stage_number in self.checkpoints and 
                self.checkpoints[stage_number].status == "completed")
    
    def get_checkpoint_info(self, stage_number: int) -> Optional[CheckpointInfo]:
        """Get checkpoint information for a stage."""
        return self.checkpoints.get(stage_number)
    
    def list_completed_stages(self) -> List[int]:
        """List all completed stages."""
        return [
            stage_num for stage_num, checkpoint in self.checkpoints.items()
            if checkpoint.status == "completed"
        ]
    
    def clear_checkpoints_from_stage(self, stage_number: int) -> None:
        """Clear checkpoints from a specific stage onwards."""
        stages_to_remove = [
            stage_num for stage_num in self.checkpoints.keys()
            if stage_num >= stage_number
        ]
        
        for stage_num in stages_to_remove:
            del self.checkpoints[stage_num]
        
        self._save_checkpoints()
    
    def get_resume_stage(self) -> int:
        """Get the stage number to resume from."""
        last_completed = self.get_last_completed_stage()
        return (last_completed + 1) if last_completed is not None else 1
    
    def cleanup_temp_files(self, keep_stages: Optional[List[int]] = None) -> None:
        """Clean up temporary checkpoint files."""
        if keep_stages is None:
            keep_stages = []
        
        for file_path in self.checkpoint_dir.glob("stage_*_data.*"):
            # Extract stage number from filename
            try:
                stage_num = int(file_path.stem.split('_')[1])
                if stage_num not in keep_stages:
                    file_path.unlink()
            except (ValueError, IndexError):
                continue
