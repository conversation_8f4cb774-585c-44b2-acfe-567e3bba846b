"""
Configuration management for the primer design pipeline.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, field


@dataclass
class Config:
    """Configuration class for the primer design pipeline."""
    
    # Configuration data
    data: Dict[str, Any] = field(default_factory=dict)
    
    # File paths
    config_file: Optional[str] = None
    
    @classmethod
    def load(cls, config_file: Optional[str] = None) -> "Config":
        """Load configuration from file."""
        if config_file is None:
            # Use default config
            config_file = Path(__file__).parent.parent.parent / "config" / "default_config.yaml"
        
        config_path = Path(config_file)
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_file}")
        
        with open(config_path, 'r') as f:
            data = yaml.safe_load(f)
        
        return cls(data=data, config_file=str(config_path))
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation (e.g., 'general.max_target_genomes')."""
        keys = key.split('.')
        value = self.data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value using dot notation."""
        keys = key.split('.')
        data = self.data
        
        for k in keys[:-1]:
            if k not in data:
                data[k] = {}
            data = data[k]
        
        data[keys[-1]] = value
    
    def update(self, updates: Dict[str, Any]) -> None:
        """Update configuration with new values."""
        for key, value in updates.items():
            self.set(key, value)
    
    def save(self, output_file: str) -> None:
        """Save configuration to file."""
        with open(output_file, 'w') as f:
            yaml.dump(self.data, f, default_flow_style=False, indent=2)
    
    def create_output_dirs(self, base_dir: str) -> Dict[str, str]:
        """Create output directory structure and return paths."""
        base_path = Path(base_dir)
        
        dirs = {
            'base': str(base_path),
            'genomes': str(base_path / 'genomes'),
            'quality': str(base_path / 'quality_assessment'),
            'ani': str(base_path / 'ani_analysis'),
            'kmers': str(base_path / 'kmer_analysis'),
            'regions': str(base_path / 'genomic_regions'),
            'primers': str(base_path / 'primer_design'),
            'checkpoints': str(base_path / 'checkpoints'),
            'logs': str(base_path / 'logs'),
            'temp': str(base_path / 'temp')
        }
        
        for dir_path in dirs.values():
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        return dirs
    
    def __getitem__(self, key: str) -> Any:
        """Allow dictionary-style access."""
        return self.get(key)
    
    def __setitem__(self, key: str, value: Any) -> None:
        """Allow dictionary-style assignment."""
        self.set(key, value)
    
    def __contains__(self, key: str) -> bool:
        """Check if key exists in configuration."""
        return self.get(key) is not None
