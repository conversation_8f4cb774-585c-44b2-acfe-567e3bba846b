"""
Main pipeline controller for species-specific primer design.
"""

from typing import Optional, Dict, Any
from pathlib import Path
import sys

from .config import Config
from .checkpoint import CheckpointManager
from .utils.logger import setup_logger
from .stages import (
    GenomeDownloadStage,
    RelatedSpeciesStage, 
    QualityAssessmentStage,
    ANIAnalysisStage,
    KmerAnalysisStage,
    RegionIdentificationStage,
    PrimerDesignStage
)


class PrimerPipeline:
    """Main pipeline controller for species-specific primer design."""
    
    def __init__(self, species_name: str, config_file: Optional[str] = None,
                 output_dir: Optional[str] = None, start_stage: int = 1,
                 resume: bool = False):
        """Initialize the pipeline."""
        self.species_name = species_name
        self.start_stage = start_stage
        self.resume = resume
        
        # Load configuration
        self.config = Config.load(config_file)
        
        # Override output directory if specified
        if output_dir:
            self.config.set('general.output_dir', output_dir)
        
        # Create output directories
        self.output_dirs = self.config.create_output_dirs(
            self.config.get('general.output_dir', 'output')
        )
        
        # Setup logging
        log_file = Path(self.output_dirs['logs']) / f"{species_name.replace(' ', '_')}_pipeline.log"
        self.logger = setup_logger(
            level=self.config.get('general.log_level', 'INFO'),
            log_file=str(log_file)
        )
        
        # Setup checkpoint manager
        self.checkpoint_manager = CheckpointManager(
            checkpoint_dir=self.output_dirs['checkpoints'],
            compress=self.config.get('checkpoints.compress_checkpoints', True)
        )
        
        # Initialize stages
        self.stages = self._initialize_stages()
        
        # Determine starting stage
        if resume:
            self.start_stage = self.checkpoint_manager.get_resume_stage()
            self.logger.info(f"Resuming pipeline from stage {self.start_stage}")
    
    def _initialize_stages(self) -> Dict[int, Any]:
        """Initialize all pipeline stages."""
        stages = {}
        
        stages[1] = GenomeDownloadStage(
            self.config, self.checkpoint_manager, self.logger, 1, "Genome Download & Selection"
        )
        stages[2] = RelatedSpeciesStage(
            self.config, self.checkpoint_manager, self.logger, 2, "Related Species Collection"
        )
        stages[3] = QualityAssessmentStage(
            self.config, self.checkpoint_manager, self.logger, 3, "Quality Assessment & Filtering"
        )
        stages[4] = ANIAnalysisStage(
            self.config, self.checkpoint_manager, self.logger, 4, "Taxonomic Validation via ANI"
        )
        stages[5] = KmerAnalysisStage(
            self.config, self.checkpoint_manager, self.logger, 5, "Species-Specific K-mer Analysis"
        )
        stages[6] = RegionIdentificationStage(
            self.config, self.checkpoint_manager, self.logger, 6, "Genomic Region Identification"
        )
        stages[7] = PrimerDesignStage(
            self.config, self.checkpoint_manager, self.logger, 7, "Primer Design"
        )
        
        return stages
    
    def run(self, force_rerun: bool = False) -> Dict[str, Any]:
        """Run the complete pipeline."""
        self.logger.info(f"Starting Species-Specific Primer Design Pipeline")
        self.logger.info(f"Target species: {self.species_name}")
        self.logger.info(f"Starting from stage: {self.start_stage}")
        self.logger.info(f"Output directory: {self.output_dirs['base']}")
        
        # Pipeline data flow
        pipeline_data = {"species_name": self.species_name}
        
        try:
            # Stage 1: Genome Download & Selection
            if self.start_stage <= 1:
                genome_data = self.stages[1].execute(pipeline_data, force_rerun)
                pipeline_data.update(genome_data)
            else:
                # Load from checkpoint
                genome_data = self.stages[1].load_checkpoint_data()
                pipeline_data.update(genome_data)
            
            # Stage 2: Related Species Collection
            if self.start_stage <= 2:
                related_data = self.stages[2].execute(pipeline_data, force_rerun)
                pipeline_data.update(related_data)
            else:
                related_data = self.stages[2].load_checkpoint_data()
                pipeline_data.update(related_data)
            
            # Stage 3: Quality Assessment & Filtering
            if self.start_stage <= 3:
                quality_data = self.stages[3].execute(pipeline_data, force_rerun)
                pipeline_data.update(quality_data)
            else:
                quality_data = self.stages[3].load_checkpoint_data()
                pipeline_data.update(quality_data)
            
            # Stage 4: Taxonomic Validation via ANI
            if self.start_stage <= 4:
                ani_data = self.stages[4].execute(pipeline_data, force_rerun)
                pipeline_data.update(ani_data)
            else:
                ani_data = self.stages[4].load_checkpoint_data()
                pipeline_data.update(ani_data)
            
            # Stage 5: Species-Specific K-mer Analysis
            if self.start_stage <= 5:
                kmer_data = self.stages[5].execute(pipeline_data, force_rerun)
                pipeline_data.update(kmer_data)
            else:
                kmer_data = self.stages[5].load_checkpoint_data()
                pipeline_data.update(kmer_data)
            
            # Stage 6: Genomic Region Identification
            if self.start_stage <= 6:
                region_data = self.stages[6].execute(pipeline_data, force_rerun)
                pipeline_data.update(region_data)
            else:
                region_data = self.stages[6].load_checkpoint_data()
                pipeline_data.update(region_data)
            
            # Stage 7: Primer Design
            if self.start_stage <= 7:
                primer_data = self.stages[7].execute(pipeline_data, force_rerun)
                pipeline_data.update(primer_data)
            
            # Generate final report
            self._generate_final_report(pipeline_data)
            
            self.logger.info("Pipeline completed successfully!")
            return pipeline_data
            
        except Exception as e:
            self.logger.error(f"Pipeline failed: {str(e)}")
            raise
        
        finally:
            # Cleanup if configured
            if not self.config.get('checkpoints.cleanup_temp_files', False):
                self.checkpoint_manager.cleanup_temp_files()
    
    def _generate_final_report(self, pipeline_data: Dict[str, Any]) -> None:
        """Generate final pipeline report."""
        import json
        from datetime import datetime
        
        report = {
            "species_name": self.species_name,
            "pipeline_version": "1.0.0",
            "completion_time": datetime.now().isoformat(),
            "configuration": self.config.data,
            "stages_completed": self.checkpoint_manager.list_completed_stages(),
            "output_directories": self.output_dirs,
            "summary": {
                "total_target_genomes": pipeline_data.get("total_target_genomes", 0),
                "total_related_genomes": pipeline_data.get("total_related_genomes", 0),
                "genomes_after_quality_filter": pipeline_data.get("genomes_after_quality_filter", 0),
                "primer_pairs_designed": len(pipeline_data.get("primer_pairs", [])),
                "top_primer_pair": pipeline_data.get("primer_pairs", [{}])[0] if pipeline_data.get("primer_pairs") else None
            }
        }
        
        report_file = Path(self.output_dirs['base']) / "pipeline_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        self.logger.info(f"Final report saved: {report_file}")
    
    def get_stage_info(self, stage_number: int) -> Optional[Dict[str, Any]]:
        """Get information about a specific stage."""
        if stage_number in self.stages:
            checkpoint_info = self.checkpoint_manager.get_checkpoint_info(stage_number)
            return {
                "stage_number": stage_number,
                "stage_name": self.stages[stage_number].stage_name,
                "completed": self.checkpoint_manager.is_stage_completed(stage_number),
                "checkpoint_info": checkpoint_info
            }
        return None
    
    def list_stages(self) -> Dict[int, str]:
        """List all pipeline stages."""
        return {num: stage.stage_name for num, stage in self.stages.items()}
