"""
Stage 1: Genome Download & Selection

Downloads genomes for the target species using NCBI datasets and selects
the top genomes based on quality metrics.
"""

import json
import pandas as pd
from pathlib import Path
from typing import Any, Dict, List, Optional
import subprocess
import shutil

from .base_stage import BaseStage


class GenomeDownloadStage(BaseStage):
    """Stage 1: Download and select high-quality genomes for target species."""
    
    def validate_input(self, input_data: Any) -> bool:
        """Validate input data contains species name."""
        return (isinstance(input_data, dict) and 
                'species_name' in input_data and 
                input_data['species_name'])
    
    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Download and select genomes for target species."""
        species_name = input_data['species_name']
        config = self.get_config_section('genome_download')
        
        # Check required tools
        self.check_required_tools(['datasets', 'dataformat'])
        
        self.logger.info(f"Downloading genomes for species: {species_name}")
        
        # Step 1: Search for available genomes
        genome_metadata = self._search_genomes(species_name, config)
        
        if not genome_metadata:
            raise ValueError(f"No genomes found for species: {species_name}")
        
        self.logger.info(f"Found {len(genome_metadata)} genomes for {species_name}")
        
        # Step 2: Filter and rank genomes
        filtered_genomes = self._filter_and_rank_genomes(genome_metadata, config)
        
        # Step 3: Select top genomes
        max_genomes = self.config.get('general.max_target_genomes', 100)
        selected_genomes = filtered_genomes.head(max_genomes)
        
        self.logger.info(f"Selected {len(selected_genomes)} genomes for download")
        
        # Step 4: Download selected genomes
        download_dir = self.stage_dir / "target_genomes"
        download_dir.mkdir(exist_ok=True)
        
        downloaded_genomes = self._download_genomes(selected_genomes, download_dir)
        
        # Step 5: Save metadata and create summary
        metadata_file = self.create_stage_file("genome_metadata.csv")
        selected_genomes.to_csv(metadata_file, index=False)
        
        summary = self._create_summary(selected_genomes, downloaded_genomes)
        self.save_stage_report(summary, "download_summary.json")
        
        return {
            "target_genomes": downloaded_genomes,
            "target_genome_metadata": selected_genomes.to_dict('records'),
            "total_target_genomes": len(downloaded_genomes),
            "download_summary": summary
        }
    
    def _search_genomes(self, species_name: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search for genomes using NCBI datasets."""
        self.logger.info("Searching NCBI for available genomes...")
        
        # Build datasets command
        cmd = [
            'datasets', 'summary', 'genome', 'taxon', f'"{species_name}"',
            '--as-json-lines'
        ]
        
        # Add filters
        if config.get('assembly_level'):
            levels = ','.join(config['assembly_level'])
            cmd.extend(['--assembly-level', levels])
        
        if config.get('refseq_category'):
            categories = ','.join([cat for cat in config['refseq_category'] if cat != 'na'])
            if categories:
                cmd.extend(['--refseq-category', categories])
        
        if config.get('exclude_partial', True):
            cmd.append('--exclude-partial')
        
        if config.get('exclude_anomalous', True):
            cmd.append('--exclude-anomalous')
        
        # Run command
        try:
            result = subprocess.run(
                ' '.join(cmd), shell=True, capture_output=True, text=True, check=True
            )
            
            # Parse JSON lines output
            genomes = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        genome_data = json.loads(line)
                        genomes.append(genome_data)
                    except json.JSONDecodeError:
                        continue
            
            return genomes
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to search genomes: {e.stderr}")
            raise
    
    def _filter_and_rank_genomes(self, genome_metadata: List[Dict[str, Any]], 
                                config: Dict[str, Any]) -> pd.DataFrame:
        """Filter and rank genomes by quality metrics."""
        self.logger.info("Filtering and ranking genomes by quality...")
        
        # Convert to DataFrame
        df = pd.DataFrame(genome_metadata)
        
        # Extract relevant fields
        processed_genomes = []
        for _, genome in df.iterrows():
            try:
                assembly_info = genome.get('assembly_info', {})
                assembly_stats = assembly_info.get('assembly_stats', {})
                
                processed_genome = {
                    'accession': genome.get('accession'),
                    'organism_name': genome.get('organism', {}).get('organism_name'),
                    'assembly_level': assembly_info.get('assembly_level'),
                    'refseq_category': assembly_info.get('refseq_category'),
                    'total_sequence_length': assembly_stats.get('total_sequence_length', 0),
                    'number_of_contigs': assembly_stats.get('number_of_contigs', 0),
                    'contig_n50': assembly_stats.get('contig_n50', 0),
                    'gc_percent': assembly_stats.get('gc_percent', 0),
                    'submission_date': assembly_info.get('submission_date'),
                    'sequencing_tech': assembly_info.get('sequencing_tech', []),
                }
                
                # Calculate quality score
                processed_genome['quality_score'] = self._calculate_quality_score(processed_genome)
                
                processed_genomes.append(processed_genome)
                
            except Exception as e:
                self.logger.warning(f"Error processing genome {genome.get('accession', 'unknown')}: {e}")
                continue
        
        df_processed = pd.DataFrame(processed_genomes)
        
        # Filter by basic criteria
        initial_count = len(df_processed)
        
        # Remove genomes with missing essential data
        df_processed = df_processed.dropna(subset=['accession', 'total_sequence_length'])
        
        # Filter by genome size
        min_size = config.get('min_genome_size', 1000000)
        max_size = config.get('max_genome_size', 20000000)
        df_processed = df_processed[
            (df_processed['total_sequence_length'] >= min_size) &
            (df_processed['total_sequence_length'] <= max_size)
        ]
        
        self.logger.info(f"Filtered genomes: {initial_count} -> {len(df_processed)}")
        
        # Sort by quality score (descending)
        df_processed = df_processed.sort_values('quality_score', ascending=False)
        
        return df_processed
    
    def _calculate_quality_score(self, genome: Dict[str, Any]) -> float:
        """Calculate quality score for genome ranking."""
        score = 0.0
        
        # Assembly level scoring
        level_scores = {
            'Complete Genome': 100,
            'Chromosome': 80,
            'Scaffold': 60,
            'Contig': 40
        }
        score += level_scores.get(genome.get('assembly_level'), 0)
        
        # RefSeq category scoring
        refseq_scores = {
            'reference genome': 50,
            'representative genome': 30,
            'na': 10
        }
        score += refseq_scores.get(genome.get('refseq_category'), 0)
        
        # N50 scoring (normalized)
        n50 = genome.get('contig_n50', 0)
        if n50 > 0:
            score += min(50, n50 / 100000)  # Max 50 points for N50 > 5Mb
        
        # Contig count scoring (fewer is better)
        contig_count = genome.get('number_of_contigs', float('inf'))
        if contig_count <= 10:
            score += 30
        elif contig_count <= 100:
            score += 20
        elif contig_count <= 1000:
            score += 10
        
        return score
    
    def _download_genomes(self, selected_genomes: pd.DataFrame, 
                         download_dir: Path) -> List[str]:
        """Download selected genomes."""
        self.logger.info(f"Downloading {len(selected_genomes)} genomes...")
        
        downloaded_files = []
        accessions = selected_genomes['accession'].tolist()
        
        # Create accession list file
        accession_file = self.stage_dir / "accessions.txt"
        with open(accession_file, 'w') as f:
            for acc in accessions:
                f.write(f"{acc}\n")
        
        # Download using datasets
        cmd = [
            'datasets', 'download', 'genome', 'accession',
            '--inputfile', str(accession_file),
            '--filename', str(download_dir / 'genomes.zip'),
            '--include', 'genome'
        ]
        
        try:
            self.logger.info("Running datasets download command...")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Extract downloaded files
            import zipfile
            zip_file = download_dir / 'genomes.zip'
            
            if zip_file.exists():
                with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                    zip_ref.extractall(download_dir)
                
                # Find extracted genome files
                for genome_dir in (download_dir / 'ncbi_dataset' / 'data').glob('*'):
                    if genome_dir.is_dir():
                        for fasta_file in genome_dir.glob('*.fna'):
                            # Rename to accession.fna
                            new_name = download_dir / f"{genome_dir.name}.fna"
                            shutil.copy2(fasta_file, new_name)
                            downloaded_files.append(str(new_name))
                
                # Cleanup
                zip_file.unlink()
                shutil.rmtree(download_dir / 'ncbi_dataset', ignore_errors=True)
            
            self.logger.info(f"Successfully downloaded {len(downloaded_files)} genome files")
            return downloaded_files
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to download genomes: {e.stderr}")
            raise
    
    def _create_summary(self, selected_genomes: pd.DataFrame, 
                       downloaded_files: List[str]) -> Dict[str, Any]:
        """Create download summary."""
        return {
            "total_genomes_found": len(selected_genomes),
            "genomes_downloaded": len(downloaded_files),
            "assembly_levels": selected_genomes['assembly_level'].value_counts().to_dict(),
            "refseq_categories": selected_genomes['refseq_category'].value_counts().to_dict(),
            "genome_size_stats": {
                "mean": float(selected_genomes['total_sequence_length'].mean()),
                "median": float(selected_genomes['total_sequence_length'].median()),
                "min": int(selected_genomes['total_sequence_length'].min()),
                "max": int(selected_genomes['total_sequence_length'].max())
            },
            "quality_score_stats": {
                "mean": float(selected_genomes['quality_score'].mean()),
                "median": float(selected_genomes['quality_score'].median()),
                "min": float(selected_genomes['quality_score'].min()),
                "max": float(selected_genomes['quality_score'].max())
            }
        }
    
    def get_output_files(self) -> List[str]:
        """Get list of output files."""
        return [
            str(self.create_stage_file("genome_metadata.csv")),
            str(self.create_stage_file("download_summary.json")),
            str(self.stage_dir / "target_genomes")
        ]
