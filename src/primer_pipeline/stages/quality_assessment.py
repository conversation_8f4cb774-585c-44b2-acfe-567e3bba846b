"""
Stage 3: Quality Assessment & Filtering

Evaluates genome quality using multiple metrics and filters out problematic genomes.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from Bio import SeqIO
from Bio.SeqUtils import GC
import matplotlib.pyplot as plt
import seaborn as sns

from .base_stage import BaseStage


class QualityAssessmentStage(BaseStage):
    """Stage 3: Assess and filter genomes by quality metrics."""
    
    def validate_input(self, input_data: Any) -> bool:
        """Validate input data contains genome files."""
        return (isinstance(input_data, dict) and 
                'target_genomes' in input_data and
                'related_genomes' in input_data)
    
    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess quality and filter genomes."""
        target_genomes = input_data['target_genomes']
        related_genomes = input_data['related_genomes']
        config = self.get_config_section('quality_assessment')
        
        self.logger.info("Starting genome quality assessment...")
        
        # Step 1: Calculate quality metrics for all genomes
        target_metrics = self._calculate_genome_metrics(target_genomes, "target")
        related_metrics = self._calculate_genome_metrics(related_genomes, "related")
        
        all_metrics = target_metrics + related_metrics
        metrics_df = pd.DataFrame(all_metrics)
        
        self.logger.info(f"Calculated metrics for {len(all_metrics)} genomes")
        
        # Step 2: Apply quality filters
        filtered_df, filter_report = self._apply_quality_filters(metrics_df, config)
        
        # Step 3: Separate filtered genomes
        filtered_target = filtered_df[filtered_df['genome_type'] == 'target']
        filtered_related = filtered_df[filtered_df['genome_type'] == 'related']
        
        # Step 4: Generate quality plots
        self._generate_quality_plots(metrics_df, filtered_df)
        
        # Step 5: Create quality report
        quality_report = self._create_quality_report(
            metrics_df, filtered_df, filter_report
        )
        
        # Step 6: Save results
        metrics_file = self.create_stage_file("genome_metrics.csv")
        metrics_df.to_csv(metrics_file, index=False)
        
        filtered_file = self.create_stage_file("filtered_genomes.csv")
        filtered_df.to_csv(filtered_file, index=False)
        
        self.save_stage_report(quality_report, "quality_report.json")
        
        return {
            "filtered_target_genomes": filtered_target['file_path'].tolist(),
            "filtered_related_genomes": filtered_related['file_path'].tolist(),
            "target_genome_metrics": filtered_target.to_dict('records'),
            "related_genome_metrics": filtered_related.to_dict('records'),
            "genomes_after_quality_filter": len(filtered_df),
            "quality_report": quality_report
        }
    
    def _calculate_genome_metrics(self, genome_files: List[str], 
                                genome_type: str) -> List[Dict[str, Any]]:
        """Calculate quality metrics for a set of genomes."""
        metrics = []
        
        for i, genome_file in enumerate(genome_files):
            self.log_progress(i + 1, len(genome_files), 
                            f"Analyzing {genome_type} genome {Path(genome_file).name}")
            
            try:
                genome_metrics = self._analyze_single_genome(genome_file, genome_type)
                metrics.append(genome_metrics)
            except Exception as e:
                self.logger.warning(f"Failed to analyze {genome_file}: {e}")
                continue
        
        return metrics
    
    def _analyze_single_genome(self, genome_file: str, genome_type: str) -> Dict[str, Any]:
        """Analyze a single genome file."""
        sequences = list(SeqIO.parse(genome_file, "fasta"))
        
        if not sequences:
            raise ValueError(f"No sequences found in {genome_file}")
        
        # Basic statistics
        total_length = sum(len(seq) for seq in sequences)
        num_contigs = len(sequences)
        contig_lengths = [len(seq) for seq in sequences]
        contig_lengths.sort(reverse=True)
        
        # N50 calculation
        n50 = self._calculate_n50(contig_lengths)
        
        # GC content
        total_sequence = ''.join(str(seq.seq) for seq in sequences)
        gc_content = GC(total_sequence)
        
        # N content
        n_count = total_sequence.upper().count('N')
        n_content = n_count / total_length if total_length > 0 else 0
        
        # Longest contig
        longest_contig = max(contig_lengths) if contig_lengths else 0
        
        # Contig statistics
        mean_contig_length = np.mean(contig_lengths) if contig_lengths else 0
        median_contig_length = np.median(contig_lengths) if contig_lengths else 0
        
        return {
            'file_path': genome_file,
            'genome_type': genome_type,
            'accession': Path(genome_file).stem,
            'total_length': total_length,
            'num_contigs': num_contigs,
            'n50': n50,
            'gc_content': gc_content,
            'n_content': n_content,
            'longest_contig': longest_contig,
            'mean_contig_length': mean_contig_length,
            'median_contig_length': median_contig_length,
            'shortest_contig': min(contig_lengths) if contig_lengths else 0
        }
    
    def _calculate_n50(self, contig_lengths: List[int]) -> int:
        """Calculate N50 statistic."""
        if not contig_lengths:
            return 0
        
        total_length = sum(contig_lengths)
        target_length = total_length / 2
        
        cumulative_length = 0
        for length in contig_lengths:
            cumulative_length += length
            if cumulative_length >= target_length:
                return length
        
        return 0
    
    def _apply_quality_filters(self, metrics_df: pd.DataFrame, 
                             config: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """Apply quality filters to genomes."""
        self.logger.info("Applying quality filters...")
        
        initial_count = len(metrics_df)
        filter_report = {"initial_count": initial_count, "filters_applied": []}
        
        # Make a copy for filtering
        filtered_df = metrics_df.copy()
        
        # Filter 1: Genome size
        min_size = config.get('min_genome_size', 1000000)
        max_size = config.get('max_genome_size', 20000000)
        
        size_mask = (filtered_df['total_length'] >= min_size) & (filtered_df['total_length'] <= max_size)
        removed_count = len(filtered_df) - size_mask.sum()
        filtered_df = filtered_df[size_mask]
        
        filter_report["filters_applied"].append({
            "filter": "genome_size",
            "removed": removed_count,
            "remaining": len(filtered_df)
        })
        
        # Filter 2: Genome size outliers (within each genome type)
        outlier_threshold = config.get('genome_size_outlier_threshold', 3.0)
        outlier_mask = pd.Series(True, index=filtered_df.index)
        
        for genome_type in ['target', 'related']:
            type_mask = filtered_df['genome_type'] == genome_type
            if type_mask.sum() > 0:
                type_data = filtered_df[type_mask]['total_length']
                mean_size = type_data.mean()
                std_size = type_data.std()
                
                if std_size > 0:
                    z_scores = np.abs((type_data - mean_size) / std_size)
                    type_outliers = z_scores > outlier_threshold
                    outlier_mask[type_data.index[type_outliers]] = False
        
        removed_count = len(filtered_df) - outlier_mask.sum()
        filtered_df = filtered_df[outlier_mask]
        
        filter_report["filters_applied"].append({
            "filter": "size_outliers",
            "removed": removed_count,
            "remaining": len(filtered_df)
        })
        
        # Filter 3: GC content outliers
        gc_threshold = config.get('gc_content_outlier_threshold', 3.0)
        gc_outlier_mask = pd.Series(True, index=filtered_df.index)
        
        for genome_type in ['target', 'related']:
            type_mask = filtered_df['genome_type'] == genome_type
            if type_mask.sum() > 0:
                type_data = filtered_df[type_mask]['gc_content']
                mean_gc = type_data.mean()
                std_gc = type_data.std()
                
                if std_gc > 0:
                    z_scores = np.abs((type_data - mean_gc) / std_gc)
                    type_outliers = z_scores > gc_threshold
                    gc_outlier_mask[type_data.index[type_outliers]] = False
        
        removed_count = len(filtered_df) - gc_outlier_mask.sum()
        filtered_df = filtered_df[gc_outlier_mask]
        
        filter_report["filters_applied"].append({
            "filter": "gc_outliers",
            "removed": removed_count,
            "remaining": len(filtered_df)
        })
        
        # Filter 4: N50 threshold
        min_n50 = config.get('min_n50', 10000)
        n50_mask = filtered_df['n50'] >= min_n50
        removed_count = len(filtered_df) - n50_mask.sum()
        filtered_df = filtered_df[n50_mask]
        
        filter_report["filters_applied"].append({
            "filter": "min_n50",
            "removed": removed_count,
            "remaining": len(filtered_df)
        })
        
        # Filter 5: Maximum contigs
        max_contigs = config.get('max_contigs', 1000)
        contig_mask = filtered_df['num_contigs'] <= max_contigs
        removed_count = len(filtered_df) - contig_mask.sum()
        filtered_df = filtered_df[contig_mask]
        
        filter_report["filters_applied"].append({
            "filter": "max_contigs",
            "removed": removed_count,
            "remaining": len(filtered_df)
        })
        
        # Filter 6: N content
        max_n_content = config.get('max_n_content', 0.05)
        n_mask = filtered_df['n_content'] <= max_n_content
        removed_count = len(filtered_df) - n_mask.sum()
        filtered_df = filtered_df[n_mask]
        
        filter_report["filters_applied"].append({
            "filter": "max_n_content",
            "removed": removed_count,
            "remaining": len(filtered_df)
        })
        
        filter_report["final_count"] = len(filtered_df)
        filter_report["total_removed"] = initial_count - len(filtered_df)
        
        self.logger.info(f"Quality filtering: {initial_count} -> {len(filtered_df)} genomes")
        
        return filtered_df, filter_report
    
    def _generate_quality_plots(self, original_df: pd.DataFrame, 
                              filtered_df: pd.DataFrame) -> None:
        """Generate quality assessment plots."""
        self.logger.info("Generating quality plots...")
        
        # Set up the plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Genome Quality Assessment', fontsize=16)
        
        # Plot 1: Genome size distribution
        ax = axes[0, 0]
        for genome_type in ['target', 'related']:
            orig_data = original_df[original_df['genome_type'] == genome_type]['total_length'] / 1e6
            filt_data = filtered_df[filtered_df['genome_type'] == genome_type]['total_length'] / 1e6
            
            ax.hist(orig_data, alpha=0.5, label=f'{genome_type} (original)', bins=20)
            ax.hist(filt_data, alpha=0.7, label=f'{genome_type} (filtered)', bins=20)
        
        ax.set_xlabel('Genome Size (Mb)')
        ax.set_ylabel('Count')
        ax.set_title('Genome Size Distribution')
        ax.legend()
        
        # Plot 2: GC content distribution
        ax = axes[0, 1]
        for genome_type in ['target', 'related']:
            orig_data = original_df[original_df['genome_type'] == genome_type]['gc_content']
            filt_data = filtered_df[filtered_df['genome_type'] == genome_type]['gc_content']
            
            ax.hist(orig_data, alpha=0.5, label=f'{genome_type} (original)', bins=20)
            ax.hist(filt_data, alpha=0.7, label=f'{genome_type} (filtered)', bins=20)
        
        ax.set_xlabel('GC Content (%)')
        ax.set_ylabel('Count')
        ax.set_title('GC Content Distribution')
        ax.legend()
        
        # Plot 3: N50 distribution
        ax = axes[0, 2]
        for genome_type in ['target', 'related']:
            orig_data = original_df[original_df['genome_type'] == genome_type]['n50'] / 1000
            filt_data = filtered_df[filtered_df['genome_type'] == genome_type]['n50'] / 1000
            
            ax.hist(orig_data, alpha=0.5, label=f'{genome_type} (original)', bins=20)
            ax.hist(filt_data, alpha=0.7, label=f'{genome_type} (filtered)', bins=20)
        
        ax.set_xlabel('N50 (kb)')
        ax.set_ylabel('Count')
        ax.set_title('N50 Distribution')
        ax.legend()
        
        # Plot 4: Number of contigs
        ax = axes[1, 0]
        for genome_type in ['target', 'related']:
            orig_data = original_df[original_df['genome_type'] == genome_type]['num_contigs']
            filt_data = filtered_df[filtered_df['genome_type'] == genome_type]['num_contigs']
            
            ax.hist(orig_data, alpha=0.5, label=f'{genome_type} (original)', bins=20)
            ax.hist(filt_data, alpha=0.7, label=f'{genome_type} (filtered)', bins=20)
        
        ax.set_xlabel('Number of Contigs')
        ax.set_ylabel('Count')
        ax.set_title('Contig Count Distribution')
        ax.legend()
        
        # Plot 5: N content
        ax = axes[1, 1]
        for genome_type in ['target', 'related']:
            orig_data = original_df[original_df['genome_type'] == genome_type]['n_content'] * 100
            filt_data = filtered_df[filtered_df['genome_type'] == genome_type]['n_content'] * 100
            
            ax.hist(orig_data, alpha=0.5, label=f'{genome_type} (original)', bins=20)
            ax.hist(filt_data, alpha=0.7, label=f'{genome_type} (filtered)', bins=20)
        
        ax.set_xlabel('N Content (%)')
        ax.set_ylabel('Count')
        ax.set_title('N Content Distribution')
        ax.legend()
        
        # Plot 6: Quality scatter plot
        ax = axes[1, 2]
        for genome_type in ['target', 'related']:
            data = filtered_df[filtered_df['genome_type'] == genome_type]
            if len(data) > 0:
                ax.scatter(data['total_length'] / 1e6, data['n50'] / 1000,
                          alpha=0.6, label=genome_type)

        ax.set_xlabel('Genome Size (Mb)')
        ax.set_ylabel('N50 (kb)')
        ax.set_title('Genome Size vs N50')
        ax.legend()

        plt.tight_layout()

        # Save plot
        plot_file = self.create_stage_file("quality_plots.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()

        self.logger.info(f"Quality plots saved: {plot_file}")
    
    def _create_quality_report(self, original_df: pd.DataFrame, 
                             filtered_df: pd.DataFrame,
                             filter_report: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive quality report."""
        
        def get_stats(df: pd.DataFrame, genome_type: str) -> Dict[str, Any]:
            type_data = df[df['genome_type'] == genome_type]
            if len(type_data) == 0:
                return {}
            
            return {
                "count": len(type_data),
                "genome_size": {
                    "mean": float(type_data['total_length'].mean()),
                    "median": float(type_data['total_length'].median()),
                    "std": float(type_data['total_length'].std()),
                    "min": int(type_data['total_length'].min()),
                    "max": int(type_data['total_length'].max())
                },
                "gc_content": {
                    "mean": float(type_data['gc_content'].mean()),
                    "median": float(type_data['gc_content'].median()),
                    "std": float(type_data['gc_content'].std()),
                    "min": float(type_data['gc_content'].min()),
                    "max": float(type_data['gc_content'].max())
                },
                "n50": {
                    "mean": float(type_data['n50'].mean()),
                    "median": float(type_data['n50'].median()),
                    "std": float(type_data['n50'].std()),
                    "min": int(type_data['n50'].min()),
                    "max": int(type_data['n50'].max())
                },
                "num_contigs": {
                    "mean": float(type_data['num_contigs'].mean()),
                    "median": float(type_data['num_contigs'].median()),
                    "std": float(type_data['num_contigs'].std()),
                    "min": int(type_data['num_contigs'].min()),
                    "max": int(type_data['num_contigs'].max())
                }
            }
        
        return {
            "filter_summary": filter_report,
            "original_stats": {
                "target": get_stats(original_df, "target"),
                "related": get_stats(original_df, "related")
            },
            "filtered_stats": {
                "target": get_stats(filtered_df, "target"),
                "related": get_stats(filtered_df, "related")
            },
            "quality_thresholds_used": self.get_config_section('quality_assessment')
        }
    
    def get_output_files(self) -> List[str]:
        """Get list of output files."""
        return [
            str(self.create_stage_file("genome_metrics.csv")),
            str(self.create_stage_file("filtered_genomes.csv")),
            str(self.create_stage_file("quality_report.json")),
            str(self.create_stage_file("quality_plots.png"))
        ]
