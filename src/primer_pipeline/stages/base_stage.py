"""
Base class for pipeline stages.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from pathlib import Path
import time

from ..config import Config
from ..checkpoint import CheckpointManager
from ..utils.logger import PipelineLogger


class BaseStage(ABC):
    """Base class for all pipeline stages."""
    
    def __init__(self, config: Config, checkpoint_manager: CheckpointManager, 
                 logger: PipelineLogger, stage_number: int, stage_name: str):
        """Initialize base stage."""
        self.config = config
        self.checkpoint_manager = checkpoint_manager
        self.logger = logger
        self.stage_number = stage_number
        self.stage_name = stage_name
        
        # Stage-specific directories
        self.output_dirs = config.create_output_dirs(config.get('general.output_dir', 'output'))
        self.stage_dir = Path(self.output_dirs['base']) / f"stage_{stage_number:02d}_{stage_name.lower().replace(' ', '_')}"
        self.stage_dir.mkdir(parents=True, exist_ok=True)
    
    @abstractmethod
    def run(self, input_data: Any = None) -> Any:
        """Run the stage. Must be implemented by subclasses."""
        pass
    
    @abstractmethod
    def validate_input(self, input_data: Any) -> bool:
        """Validate input data. Must be implemented by subclasses."""
        pass
    
    @abstractmethod
    def get_output_files(self) -> List[str]:
        """Get list of output files produced by this stage."""
        pass
    
    def execute(self, input_data: Any = None, force_rerun: bool = False) -> Any:
        """Execute the stage with error handling and checkpointing."""
        
        # Check if stage is already completed
        if not force_rerun and self.checkpoint_manager.is_stage_completed(self.stage_number):
            self.logger.info(f"Stage {self.stage_number} ({self.stage_name}) already completed. Loading from checkpoint.")
            return self.load_checkpoint_data()
        
        # Validate input
        if not self.validate_input(input_data):
            raise ValueError(f"Invalid input data for stage {self.stage_number} ({self.stage_name})")
        
        # Log stage start
        self.logger.stage_start(self.stage_name, self.stage_number)
        start_time = time.time()
        
        try:
            # Run the stage
            output_data = self.run(input_data)
            
            # Save checkpoint
            self.save_checkpoint(input_data, output_data)
            
            # Log completion
            end_time = time.time()
            duration = end_time - start_time
            self.logger.stage_complete(self.stage_name, self.stage_number)
            self.logger.info(f"Stage duration: {duration:.2f} seconds")
            
            return output_data
            
        except Exception as e:
            self.logger.error(f"Stage {self.stage_number} ({self.stage_name}) failed: {str(e)}")
            
            # Save failed checkpoint
            self.checkpoint_manager.save_checkpoint(
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                input_params=self._serialize_input(input_data),
                output_files=[],
                metadata={"error": str(e)},
                status="failed"
            )
            
            raise
    
    def save_checkpoint(self, input_data: Any, output_data: Any) -> None:
        """Save checkpoint data."""
        # Save output data
        data_file = self.checkpoint_manager.save_stage_data(
            self.stage_number, output_data
        )
        
        # Save checkpoint info
        self.checkpoint_manager.save_checkpoint(
            stage_number=self.stage_number,
            stage_name=self.stage_name,
            input_params=self._serialize_input(input_data),
            output_files=self.get_output_files(),
            metadata={"data_file": data_file}
        )
    
    def load_checkpoint_data(self) -> Any:
        """Load data from checkpoint."""
        return self.checkpoint_manager.load_stage_data(self.stage_number)
    
    def _serialize_input(self, input_data: Any) -> Dict[str, Any]:
        """Serialize input data for checkpoint storage."""
        if input_data is None:
            return {}
        elif isinstance(input_data, dict):
            return {k: str(v) for k, v in input_data.items()}
        else:
            return {"input": str(input_data)}
    
    def create_stage_file(self, filename: str) -> Path:
        """Create a file path in the stage directory."""
        return self.stage_dir / filename
    
    def log_progress(self, current: int, total: int, description: str = "") -> None:
        """Log progress for long-running operations."""
        self.logger.progress(current, total, description)
    
    def get_config_section(self, section_name: str) -> Dict[str, Any]:
        """Get configuration section for this stage."""
        return self.config.get(section_name, {})
    
    def save_stage_report(self, report_data: Dict[str, Any], filename: str = "report.json") -> None:
        """Save a stage report."""
        import json
        report_file = self.create_stage_file(filename)
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        self.logger.info(f"Stage report saved: {report_file}")
    
    def check_required_tools(self, tools: List[str]) -> None:
        """Check if required external tools are available."""
        import shutil
        missing_tools = []
        
        for tool in tools:
            if not shutil.which(tool):
                missing_tools.append(tool)
        
        if missing_tools:
            raise RuntimeError(f"Required tools not found: {', '.join(missing_tools)}")
    
    def run_command(self, command: str, check: bool = True) -> str:
        """Run a shell command and return output."""
        import subprocess
        
        self.logger.debug(f"Running command: {command}")
        
        try:
            result = subprocess.run(
                command, shell=True, capture_output=True, text=True, check=check
            )
            return result.stdout
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Command failed: {command}")
            self.logger.error(f"Error: {e.stderr}")
            raise
