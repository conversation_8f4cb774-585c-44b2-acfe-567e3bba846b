"""
Stage 6: Genomic Region Identification

Maps species-specific k-mers to genomic coordinates and identifies regions
enriched in species-specific k-mers for primer design.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, NamedTuple
from Bio import SeqIO
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

from .base_stage import BaseStage


class GenomicRegion(NamedTuple):
    """Represents a genomic region with k-mer enrichment."""
    contig: str
    start: int
    end: int
    kmer_count: int
    kmer_density: float
    conservation_score: float
    sequence: str


class RegionIdentificationStage(BaseStage):
    """Stage 6: Identify genomic regions enriched in species-specific k-mers."""
    
    def validate_input(self, input_data: Any) -> bool:
        """Validate input data contains k-mer analysis results."""
        return (isinstance(input_data, dict) and 
                'species_specific_kmers' in input_data and
                'validated_target_genomes' in input_data)
    
    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Identify genomic regions enriched in species-specific k-mers."""
        specific_kmers = set(input_data['species_specific_kmers'])
        target_genomes = input_data['validated_target_genomes']
        kmer_frequencies = input_data.get('kmer_frequencies', {})
        config = self.get_config_section('region_identification')
        
        self.logger.info("Starting genomic region identification...")
        
        if not specific_kmers:
            raise ValueError("No species-specific k-mers available for region identification")
        
        if not target_genomes:
            raise ValueError("No target genomes available for region identification")
        
        k = len(next(iter(specific_kmers)))  # Get k-mer size
        self.logger.info(f"Analyzing {len(specific_kmers)} specific k-mers of size {k}")
        
        # Step 1: Map k-mers to genomic positions in each genome
        genome_kmer_positions = self._map_kmers_to_positions(
            target_genomes, specific_kmers, k
        )
        
        # Step 2: Identify enriched regions using sliding windows
        enriched_regions = self._identify_enriched_regions(
            genome_kmer_positions, target_genomes, config
        )
        
        # Step 3: Calculate conservation scores across genomes
        conserved_regions = self._calculate_conservation_scores(
            enriched_regions, target_genomes, config
        )
        
        # Step 4: Rank and filter regions
        ranked_regions = self._rank_and_filter_regions(conserved_regions, config)
        
        # Step 5: Extract sequences for top regions
        final_regions = self._extract_region_sequences(
            ranked_regions, target_genomes, config
        )
        
        # Step 6: Generate region analysis plots
        self._generate_region_plots(genome_kmer_positions, final_regions)
        
        # Step 7: Create comprehensive report
        region_report = self._create_region_report(
            genome_kmer_positions, final_regions, config
        )
        
        # Step 8: Save results
        self._save_region_results(final_regions, region_report)
        
        return {
            "candidate_regions": [region._asdict() for region in final_regions],
            "total_candidate_regions": len(final_regions),
            "genome_kmer_positions": genome_kmer_positions,
            "region_report": region_report
        }
    
    def _map_kmers_to_positions(self, genome_files: List[str], 
                              specific_kmers: set, k: int) -> Dict[str, Dict[str, List[int]]]:
        """Map k-mers to their positions in each genome."""
        self.logger.info("Mapping k-mers to genomic positions...")
        
        genome_positions = {}
        
        for i, genome_file in enumerate(genome_files):
            self.log_progress(i + 1, len(genome_files), 
                            f"Processing {Path(genome_file).name}")
            
            genome_name = Path(genome_file).stem
            contig_positions = defaultdict(list)
            
            # Process each contig in the genome
            for record in SeqIO.parse(genome_file, "fasta"):
                contig_name = record.id
                sequence = str(record.seq).upper()
                
                # Find positions of specific k-mers
                for pos in range(len(sequence) - k + 1):
                    kmer = sequence[pos:pos + k]
                    if kmer in specific_kmers:
                        contig_positions[contig_name].append(pos)
            
            genome_positions[genome_name] = dict(contig_positions)
        
        # Calculate summary statistics
        total_positions = sum(
            len(positions) for genome_data in genome_positions.values()
            for positions in genome_data.values()
        )
        
        self.logger.info(f"Mapped {total_positions} k-mer positions across {len(genome_files)} genomes")
        return genome_positions
    
    def _identify_enriched_regions(self, genome_kmer_positions: Dict[str, Dict[str, List[int]]],
                                 genome_files: List[str], config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify regions enriched in species-specific k-mers using sliding windows."""
        self.logger.info("Identifying k-mer enriched regions...")
        
        window_size = config.get('window_size', 1000)
        step_size = config.get('step_size', 500)
        min_kmers_per_window = config.get('min_specific_kmers_per_window', 10)
        
        enriched_regions = []
        
        for genome_file in genome_files:
            genome_name = Path(genome_file).stem
            kmer_positions = genome_kmer_positions.get(genome_name, {})
            
            # Process each contig
            for record in SeqIO.parse(genome_file, "fasta"):
                contig_name = record.id
                contig_length = len(record.seq)
                positions = kmer_positions.get(contig_name, [])
                
                if not positions:
                    continue
                
                # Sliding window analysis
                for start in range(0, contig_length - window_size + 1, step_size):
                    end = start + window_size
                    
                    # Count k-mers in this window
                    kmers_in_window = [pos for pos in positions if start <= pos < end]
                    kmer_count = len(kmers_in_window)
                    
                    if kmer_count >= min_kmers_per_window:
                        kmer_density = kmer_count / window_size
                        
                        enriched_regions.append({
                            'genome': genome_name,
                            'contig': contig_name,
                            'start': start,
                            'end': end,
                            'kmer_count': kmer_count,
                            'kmer_density': kmer_density,
                            'kmer_positions': kmers_in_window
                        })
        
        self.logger.info(f"Identified {len(enriched_regions)} enriched regions")
        return enriched_regions
    
    def _calculate_conservation_scores(self, enriched_regions: List[Dict[str, Any]],
                                     genome_files: List[str], 
                                     config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Calculate conservation scores for enriched regions."""
        self.logger.info("Calculating conservation scores...")
        
        min_conservation = config.get('min_conservation_across_genomes', 0.8)
        
        # Group regions by genomic location (contig and approximate position)
        region_groups = defaultdict(list)
        
        for region in enriched_regions:
            # Create a key based on contig and approximate position
            key = f"{region['contig']}_{region['start'] // 1000}"  # Group by 1kb bins
            region_groups[key].append(region)
        
        conserved_regions = []
        
        for group_key, regions in region_groups.items():
            # Calculate how many genomes have this region
            genomes_with_region = set(region['genome'] for region in regions)
            conservation_score = len(genomes_with_region) / len(genome_files)
            
            if conservation_score >= min_conservation:
                # Take the region with highest k-mer density as representative
                best_region = max(regions, key=lambda x: x['kmer_density'])
                best_region['conservation_score'] = conservation_score
                best_region['genomes_with_region'] = list(genomes_with_region)
                
                conserved_regions.append(best_region)
        
        self.logger.info(f"Found {len(conserved_regions)} conserved regions")
        return conserved_regions
    
    def _rank_and_filter_regions(self, conserved_regions: List[Dict[str, Any]],
                               config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Rank and filter regions by quality metrics."""
        self.logger.info("Ranking and filtering regions...")
        
        max_regions_per_genome = config.get('max_regions_per_genome', 100)
        
        # Calculate composite score for ranking
        for region in conserved_regions:
            # Composite score based on k-mer density and conservation
            region['composite_score'] = (
                region['kmer_density'] * 0.6 + 
                region['conservation_score'] * 0.4
            )
        
        # Sort by composite score
        ranked_regions = sorted(conserved_regions, 
                              key=lambda x: x['composite_score'], reverse=True)
        
        # Group by genome and limit per genome
        genome_region_counts = defaultdict(int)
        filtered_regions = []
        
        for region in ranked_regions:
            genome = region['genome']
            if genome_region_counts[genome] < max_regions_per_genome:
                filtered_regions.append(region)
                genome_region_counts[genome] += 1
        
        self.logger.info(f"Selected {len(filtered_regions)} top-ranked regions")
        return filtered_regions
    
    def _extract_region_sequences(self, ranked_regions: List[Dict[str, Any]],
                                genome_files: List[str], 
                                config: Dict[str, Any]) -> List[GenomicRegion]:
        """Extract sequences for the selected regions."""
        self.logger.info("Extracting sequences for selected regions...")
        
        extension = config.get('region_extension', 200)
        
        # Create genome file mapping
        genome_file_map = {Path(f).stem: f for f in genome_files}
        
        final_regions = []
        
        for region in ranked_regions:
            genome_name = region['genome']
            genome_file = genome_file_map.get(genome_name)
            
            if not genome_file:
                continue
            
            # Find the contig and extract sequence
            for record in SeqIO.parse(genome_file, "fasta"):
                if record.id == region['contig']:
                    contig_seq = str(record.seq).upper()
                    
                    # Extend region for primer design
                    extended_start = max(0, region['start'] - extension)
                    extended_end = min(len(contig_seq), region['end'] + extension)
                    
                    region_sequence = contig_seq[extended_start:extended_end]
                    
                    genomic_region = GenomicRegion(
                        contig=region['contig'],
                        start=extended_start,
                        end=extended_end,
                        kmer_count=region['kmer_count'],
                        kmer_density=region['kmer_density'],
                        conservation_score=region['conservation_score'],
                        sequence=region_sequence
                    )
                    
                    final_regions.append(genomic_region)
                    break
        
        self.logger.info(f"Extracted sequences for {len(final_regions)} regions")
        return final_regions
    
    def _generate_region_plots(self, genome_kmer_positions: Dict[str, Dict[str, List[int]]],
                             final_regions: List[GenomicRegion]) -> None:
        """Generate genomic region analysis plots."""
        self.logger.info("Generating region analysis plots...")
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Genomic Region Analysis', fontsize=16)
        
        # Plot 1: K-mer density distribution
        ax = axes[0, 0]
        densities = [region.kmer_density for region in final_regions]
        
        if densities:
            ax.hist(densities, bins=20, alpha=0.7, edgecolor='black')
            ax.set_xlabel('K-mer Density (k-mers/bp)')
            ax.set_ylabel('Count')
            ax.set_title('K-mer Density Distribution')
        
        # Plot 2: Conservation score distribution
        ax = axes[0, 1]
        conservation_scores = [region.conservation_score for region in final_regions]
        
        if conservation_scores:
            ax.hist(conservation_scores, bins=20, alpha=0.7, edgecolor='black')
            ax.set_xlabel('Conservation Score')
            ax.set_ylabel('Count')
            ax.set_title('Conservation Score Distribution')
        
        # Plot 3: Region length distribution
        ax = axes[1, 0]
        lengths = [region.end - region.start for region in final_regions]
        
        if lengths:
            ax.hist(lengths, bins=20, alpha=0.7, edgecolor='black')
            ax.set_xlabel('Region Length (bp)')
            ax.set_ylabel('Count')
            ax.set_title('Region Length Distribution')
        
        # Plot 4: K-mer count vs conservation
        ax = axes[1, 1]
        
        if final_regions:
            kmer_counts = [region.kmer_count for region in final_regions]
            conservation_scores = [region.conservation_score for region in final_regions]
            
            ax.scatter(kmer_counts, conservation_scores, alpha=0.6)
            ax.set_xlabel('K-mer Count')
            ax.set_ylabel('Conservation Score')
            ax.set_title('K-mer Count vs Conservation')
        
        plt.tight_layout()
        
        # Save plot
        plot_file = self.create_stage_file("region_analysis_plots.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"Region plots saved: {plot_file}")
    
    def _create_region_report(self, genome_kmer_positions: Dict[str, Dict[str, List[int]]],
                            final_regions: List[GenomicRegion],
                            config: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive region identification report."""
        
        # Calculate statistics
        if final_regions:
            kmer_densities = [region.kmer_density for region in final_regions]
            conservation_scores = [region.conservation_score for region in final_regions]
            region_lengths = [region.end - region.start for region in final_regions]
            
            density_stats = {
                "mean": float(np.mean(kmer_densities)),
                "median": float(np.median(kmer_densities)),
                "std": float(np.std(kmer_densities)),
                "min": float(np.min(kmer_densities)),
                "max": float(np.max(kmer_densities))
            }
            
            conservation_stats = {
                "mean": float(np.mean(conservation_scores)),
                "median": float(np.median(conservation_scores)),
                "std": float(np.std(conservation_scores)),
                "min": float(np.min(conservation_scores)),
                "max": float(np.max(conservation_scores))
            }
            
            length_stats = {
                "mean": float(np.mean(region_lengths)),
                "median": float(np.median(region_lengths)),
                "std": float(np.std(region_lengths)),
                "min": int(np.min(region_lengths)),
                "max": int(np.max(region_lengths))
            }
        else:
            density_stats = conservation_stats = length_stats = {}
        
        return {
            "parameters": config,
            "summary": {
                "total_candidate_regions": len(final_regions),
                "genomes_analyzed": len(genome_kmer_positions)
            },
            "statistics": {
                "kmer_density": density_stats,
                "conservation_scores": conservation_stats,
                "region_lengths": length_stats
            },
            "top_regions": [
                {
                    "contig": region.contig,
                    "start": region.start,
                    "end": region.end,
                    "length": region.end - region.start,
                    "kmer_count": region.kmer_count,
                    "kmer_density": region.kmer_density,
                    "conservation_score": region.conservation_score
                }
                for region in final_regions[:10]  # Top 10 regions
            ]
        }
    
    def _save_region_results(self, final_regions: List[GenomicRegion],
                           region_report: Dict[str, Any]) -> None:
        """Save region identification results."""
        
        # Save regions as FASTA
        fasta_file = self.create_stage_file("candidate_regions.fasta")
        with open(fasta_file, 'w') as f:
            for i, region in enumerate(final_regions):
                header = f">region_{i+1}_{region.contig}_{region.start}_{region.end}"
                f.write(f"{header}\n{region.sequence}\n")
        
        # Save regions as CSV
        csv_file = self.create_stage_file("candidate_regions.csv")
        region_data = []
        for i, region in enumerate(final_regions):
            region_data.append({
                'region_id': f"region_{i+1}",
                'contig': region.contig,
                'start': region.start,
                'end': region.end,
                'length': region.end - region.start,
                'kmer_count': region.kmer_count,
                'kmer_density': region.kmer_density,
                'conservation_score': region.conservation_score
            })
        
        pd.DataFrame(region_data).to_csv(csv_file, index=False)
        
        # Save comprehensive report
        self.save_stage_report(region_report, "region_report.json")
        
        self.logger.info("Region identification results saved")
    
    def get_output_files(self) -> List[str]:
        """Get list of output files."""
        return [
            str(self.create_stage_file("candidate_regions.fasta")),
            str(self.create_stage_file("candidate_regions.csv")),
            str(self.create_stage_file("region_report.json")),
            str(self.create_stage_file("region_analysis_plots.png"))
        ]
