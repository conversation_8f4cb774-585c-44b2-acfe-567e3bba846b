"""
Stage 4: Taxonomic Validation via ANI

Calculates Average Nucleotide Identity (ANI) between genomes to validate
taxonomic classifications and identify potential misclassifications.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
import subprocess
import tempfile
import shutil
from scipy.cluster.hierarchy import linkage, dendrogram, fcluster
from scipy.spatial.distance import squareform
import matplotlib.pyplot as plt
import seaborn as sns

from .base_stage import BaseStage


class ANIAnalysisStage(BaseStage):
    """Stage 4: Validate taxonomic classifications using ANI analysis."""
    
    def validate_input(self, input_data: Any) -> bool:
        """Validate input data contains filtered genomes."""
        return (isinstance(input_data, dict) and 
                'filtered_target_genomes' in input_data and
                'filtered_related_genomes' in input_data)
    
    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform ANI analysis and taxonomic validation."""
        target_genomes = input_data['filtered_target_genomes']
        related_genomes = input_data['filtered_related_genomes']
        config = self.get_config_section('ani_analysis')
        
        self.logger.info("Starting ANI analysis for taxonomic validation...")
        
        # Combine all genomes for analysis
        all_genomes = target_genomes + related_genomes
        genome_labels = (['target'] * len(target_genomes) + 
                        ['related'] * len(related_genomes))
        
        if len(all_genomes) < 2:
            self.logger.warning("Not enough genomes for ANI analysis")
            return self._create_empty_result()
        
        self.logger.info(f"Analyzing {len(all_genomes)} genomes ({len(target_genomes)} target, {len(related_genomes)} related)")
        
        # Step 1: Calculate ANI matrix
        ani_matrix, genome_names = self._calculate_ani_matrix(all_genomes, config)
        
        # Step 2: Analyze taxonomic consistency
        taxonomic_analysis = self._analyze_taxonomic_consistency(
            ani_matrix, genome_names, genome_labels, config
        )
        
        # Step 3: Perform clustering analysis
        clustering_results = self._perform_clustering_analysis(
            ani_matrix, genome_names, genome_labels, config
        )
        
        # Step 4: Identify misclassified genomes
        misclassified_genomes = self._identify_misclassified_genomes(
            ani_matrix, genome_names, genome_labels, config
        )
        
        # Step 5: Generate visualizations
        self._generate_ani_plots(ani_matrix, genome_names, genome_labels, clustering_results)
        
        # Step 6: Create comprehensive report
        ani_report = self._create_ani_report(
            ani_matrix, genome_names, genome_labels, taxonomic_analysis,
            clustering_results, misclassified_genomes
        )
        
        # Step 7: Save results
        self._save_ani_results(ani_matrix, genome_names, ani_report)
        
        # Filter out misclassified genomes if requested
        validated_target, validated_related = self._filter_misclassified_genomes(
            target_genomes, related_genomes, misclassified_genomes, genome_names
        )
        
        return {
            "validated_target_genomes": validated_target,
            "validated_related_genomes": validated_related,
            "ani_matrix": ani_matrix.tolist(),
            "genome_names": genome_names,
            "taxonomic_analysis": taxonomic_analysis,
            "clustering_results": clustering_results,
            "misclassified_genomes": misclassified_genomes,
            "ani_report": ani_report
        }
    
    def _calculate_ani_matrix(self, genome_files: List[str], 
                            config: Dict[str, Any]) -> Tuple[np.ndarray, List[str]]:
        """Calculate ANI matrix using FastANI."""
        self.logger.info("Calculating ANI matrix...")
        
        # Check if FastANI is available
        ani_method = config.get('ani_method', 'fastani')
        if ani_method == 'fastani':
            self.check_required_tools(['fastANI'])
        
        genome_names = [Path(f).stem for f in genome_files]
        n_genomes = len(genome_files)
        ani_matrix = np.zeros((n_genomes, n_genomes))
        
        # Create temporary directory for ANI calculations
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create file list for FastANI
            file_list = temp_path / "genome_list.txt"
            with open(file_list, 'w') as f:
                for genome_file in genome_files:
                    f.write(f"{genome_file}\n")
            
            # Run FastANI
            output_file = temp_path / "ani_results.txt"
            
            cmd = [
                'fastANI',
                '--ql', str(file_list),
                '--rl', str(file_list),
                '-o', str(output_file),
                '--fragLen', str(config.get('fragment_length', 3000)),
                '--minFraction', str(config.get('min_fraction', 0.2))
            ]
            
            try:
                self.logger.info("Running FastANI...")
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                
                # Parse results
                if output_file.exists():
                    ani_results = pd.read_csv(output_file, sep='\t', header=None,
                                            names=['query', 'reference', 'ani', 'fragments', 'total_fragments'])
                    
                    # Fill ANI matrix
                    for _, row in ani_results.iterrows():
                        query_idx = genome_files.index(row['query'])
                        ref_idx = genome_files.index(row['reference'])
                        ani_matrix[query_idx, ref_idx] = row['ani']
                
                # Fill diagonal with 100% identity
                np.fill_diagonal(ani_matrix, 100.0)
                
                self.logger.info("ANI matrix calculation completed")
                return ani_matrix, genome_names
                
            except subprocess.CalledProcessError as e:
                self.logger.error(f"FastANI failed: {e.stderr}")
                raise
    
    def _analyze_taxonomic_consistency(self, ani_matrix: np.ndarray, 
                                     genome_names: List[str], genome_labels: List[str],
                                     config: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze taxonomic consistency based on ANI thresholds."""
        self.logger.info("Analyzing taxonomic consistency...")
        
        same_species_threshold = config.get('same_species_threshold', 95.0)
        related_species_threshold = config.get('related_species_threshold', 85.0)
        
        # Analyze target-target ANI values
        target_indices = [i for i, label in enumerate(genome_labels) if label == 'target']
        target_ani_values = []
        
        for i in range(len(target_indices)):
            for j in range(i + 1, len(target_indices)):
                idx1, idx2 = target_indices[i], target_indices[j]
                ani_val = max(ani_matrix[idx1, idx2], ani_matrix[idx2, idx1])
                if ani_val > 0:
                    target_ani_values.append(ani_val)
        
        # Analyze target-related ANI values
        related_indices = [i for i, label in enumerate(genome_labels) if label == 'related']
        target_related_ani_values = []
        
        for target_idx in target_indices:
            for related_idx in related_indices:
                ani_val = max(ani_matrix[target_idx, related_idx], ani_matrix[related_idx, target_idx])
                if ani_val > 0:
                    target_related_ani_values.append(ani_val)
        
        # Calculate statistics
        target_ani_stats = self._calculate_ani_stats(target_ani_values)
        target_related_ani_stats = self._calculate_ani_stats(target_related_ani_values)
        
        # Check consistency
        consistency_issues = []
        
        # Check if target genomes are too dissimilar
        low_target_ani = [ani for ani in target_ani_values if ani < same_species_threshold]
        if low_target_ani:
            consistency_issues.append({
                "type": "low_intraspecies_ani",
                "description": f"{len(low_target_ani)} target genome pairs have ANI < {same_species_threshold}%",
                "values": low_target_ani
            })
        
        # Check if related genomes are too similar
        high_related_ani = [ani for ani in target_related_ani_values if ani >= same_species_threshold]
        if high_related_ani:
            consistency_issues.append({
                "type": "high_interspecies_ani",
                "description": f"{len(high_related_ani)} target-related pairs have ANI >= {same_species_threshold}%",
                "values": high_related_ani
            })
        
        return {
            "target_ani_stats": target_ani_stats,
            "target_related_ani_stats": target_related_ani_stats,
            "consistency_issues": consistency_issues,
            "thresholds": {
                "same_species": same_species_threshold,
                "related_species": related_species_threshold
            }
        }
    
    def _calculate_ani_stats(self, ani_values: List[float]) -> Dict[str, float]:
        """Calculate statistics for ANI values."""
        if not ani_values:
            return {"count": 0}
        
        return {
            "count": len(ani_values),
            "mean": float(np.mean(ani_values)),
            "median": float(np.median(ani_values)),
            "std": float(np.std(ani_values)),
            "min": float(np.min(ani_values)),
            "max": float(np.max(ani_values)),
            "q25": float(np.percentile(ani_values, 25)),
            "q75": float(np.percentile(ani_values, 75))
        }
    
    def _perform_clustering_analysis(self, ani_matrix: np.ndarray, 
                                   genome_names: List[str], genome_labels: List[str],
                                   config: Dict[str, Any]) -> Dict[str, Any]:
        """Perform hierarchical clustering based on ANI distances."""
        self.logger.info("Performing clustering analysis...")
        
        # Convert ANI to distance (100 - ANI)
        distance_matrix = 100 - ani_matrix
        
        # Make symmetric by taking minimum distance
        for i in range(len(distance_matrix)):
            for j in range(len(distance_matrix)):
                if i != j:
                    distance_matrix[i, j] = min(distance_matrix[i, j], distance_matrix[j, i])
        
        # Convert to condensed distance matrix for clustering
        condensed_distances = squareform(distance_matrix)
        
        # Perform hierarchical clustering
        clustering_method = config.get('clustering_method', 'average')
        linkage_matrix = linkage(condensed_distances, method=clustering_method)
        
        # Get clusters at different thresholds
        same_species_threshold = config.get('same_species_threshold', 95.0)
        related_species_threshold = config.get('related_species_threshold', 85.0)
        
        species_clusters = fcluster(linkage_matrix, 100 - same_species_threshold, criterion='distance')
        genus_clusters = fcluster(linkage_matrix, 100 - related_species_threshold, criterion='distance')
        
        return {
            "linkage_matrix": linkage_matrix.tolist(),
            "species_clusters": species_clusters.tolist(),
            "genus_clusters": genus_clusters.tolist(),
            "clustering_method": clustering_method
        }
    
    def _identify_misclassified_genomes(self, ani_matrix: np.ndarray, 
                                      genome_names: List[str], genome_labels: List[str],
                                      config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify potentially misclassified genomes."""
        self.logger.info("Identifying misclassified genomes...")
        
        same_species_threshold = config.get('same_species_threshold', 95.0)
        misclassified = []
        
        target_indices = [i for i, label in enumerate(genome_labels) if label == 'target']
        related_indices = [i for i, label in enumerate(genome_labels) if label == 'related']
        
        # Check target genomes that are too dissimilar from other targets
        for i, target_idx in enumerate(target_indices):
            other_target_indices = target_indices[:i] + target_indices[i+1:]
            
            if other_target_indices:
                max_ani_to_targets = max([
                    max(ani_matrix[target_idx, other_idx], ani_matrix[other_idx, target_idx])
                    for other_idx in other_target_indices
                    if ani_matrix[target_idx, other_idx] > 0 or ani_matrix[other_idx, target_idx] > 0
                ] or [0])
                
                if max_ani_to_targets < same_species_threshold:
                    misclassified.append({
                        "genome": genome_names[target_idx],
                        "type": "target_outlier",
                        "max_ani_to_targets": max_ani_to_targets,
                        "reason": f"ANI to other target genomes < {same_species_threshold}%"
                    })
        
        # Check related genomes that are too similar to targets
        for related_idx in related_indices:
            max_ani_to_targets = max([
                max(ani_matrix[related_idx, target_idx], ani_matrix[target_idx, related_idx])
                for target_idx in target_indices
                if ani_matrix[related_idx, target_idx] > 0 or ani_matrix[target_idx, related_idx] > 0
            ] or [0])
            
            if max_ani_to_targets >= same_species_threshold:
                misclassified.append({
                    "genome": genome_names[related_idx],
                    "type": "related_too_similar",
                    "max_ani_to_targets": max_ani_to_targets,
                    "reason": f"ANI to target genomes >= {same_species_threshold}%"
                })
        
        self.logger.info(f"Identified {len(misclassified)} potentially misclassified genomes")
        return misclassified

    def _generate_ani_plots(self, ani_matrix: np.ndarray, genome_names: List[str],
                          genome_labels: List[str], clustering_results: Dict[str, Any]) -> None:
        """Generate ANI analysis visualizations."""
        self.logger.info("Generating ANI plots...")

        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('ANI Analysis Results', fontsize=16)

        # Plot 1: ANI heatmap
        ax = axes[0, 0]

        # Create color map for genome types
        colors = ['red' if label == 'target' else 'blue' for label in genome_labels]

        # Create heatmap
        im = ax.imshow(ani_matrix, cmap='viridis', vmin=80, vmax=100)
        ax.set_title('ANI Matrix Heatmap')
        ax.set_xlabel('Genome Index')
        ax.set_ylabel('Genome Index')

        # Add colorbar
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('ANI (%)')

        # Plot 2: ANI distribution
        ax = axes[0, 1]

        # Get upper triangle values (excluding diagonal)
        upper_triangle = np.triu(ani_matrix, k=1)
        ani_values = upper_triangle[upper_triangle > 0]

        ax.hist(ani_values, bins=30, alpha=0.7, edgecolor='black')
        ax.set_xlabel('ANI (%)')
        ax.set_ylabel('Frequency')
        ax.set_title('ANI Distribution')
        ax.axvline(95, color='red', linestyle='--', label='Species threshold (95%)')
        ax.axvline(85, color='orange', linestyle='--', label='Genus threshold (85%)')
        ax.legend()

        # Plot 3: Dendrogram
        ax = axes[1, 0]

        linkage_matrix = np.array(clustering_results['linkage_matrix'])
        dendrogram(linkage_matrix, ax=ax, labels=genome_names,
                  leaf_rotation=90, leaf_font_size=8)
        ax.set_title('Hierarchical Clustering Dendrogram')
        ax.set_ylabel('Distance (100 - ANI)')

        # Plot 4: Cluster visualization
        ax = axes[1, 1]

        species_clusters = clustering_results['species_clusters']
        unique_clusters = list(set(species_clusters))

        for cluster_id in unique_clusters:
            cluster_indices = [i for i, c in enumerate(species_clusters) if c == cluster_id]
            cluster_colors = [colors[i] for i in cluster_indices]

            ax.scatter([cluster_id] * len(cluster_indices), cluster_indices,
                      c=cluster_colors, alpha=0.7, s=50)

        ax.set_xlabel('Species Cluster')
        ax.set_ylabel('Genome Index')
        ax.set_title('Species Clustering Results')

        # Add legend
        from matplotlib.patches import Patch
        legend_elements = [Patch(facecolor='red', label='Target'),
                          Patch(facecolor='blue', label='Related')]
        ax.legend(handles=legend_elements)

        plt.tight_layout()

        # Save plot
        plot_file = self.create_stage_file("ani_analysis_plots.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()

        self.logger.info(f"ANI plots saved: {plot_file}")

    def _create_ani_report(self, ani_matrix: np.ndarray, genome_names: List[str],
                         genome_labels: List[str], taxonomic_analysis: Dict[str, Any],
                         clustering_results: Dict[str, Any],
                         misclassified_genomes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create comprehensive ANI analysis report."""

        # Calculate overall statistics
        upper_triangle = np.triu(ani_matrix, k=1)
        all_ani_values = upper_triangle[upper_triangle > 0]

        return {
            "summary": {
                "total_genomes": len(genome_names),
                "target_genomes": len([l for l in genome_labels if l == 'target']),
                "related_genomes": len([l for l in genome_labels if l == 'related']),
                "ani_comparisons": len(all_ani_values),
                "misclassified_genomes": len(misclassified_genomes)
            },
            "ani_statistics": {
                "all_comparisons": self._calculate_ani_stats(all_ani_values.tolist()),
                "target_comparisons": taxonomic_analysis["target_ani_stats"],
                "target_related_comparisons": taxonomic_analysis["target_related_ani_stats"]
            },
            "taxonomic_analysis": taxonomic_analysis,
            "clustering_results": clustering_results,
            "misclassified_genomes": misclassified_genomes,
            "genome_mapping": {
                "names": genome_names,
                "labels": genome_labels
            }
        }

    def _save_ani_results(self, ani_matrix: np.ndarray, genome_names: List[str],
                        ani_report: Dict[str, Any]) -> None:
        """Save ANI analysis results."""

        # Save ANI matrix as CSV
        ani_df = pd.DataFrame(ani_matrix, index=genome_names, columns=genome_names)
        matrix_file = self.create_stage_file("ani_matrix.csv")
        ani_df.to_csv(matrix_file)

        # Save comprehensive report
        self.save_stage_report(ani_report, "ani_report.json")

        self.logger.info("ANI results saved")

    def _filter_misclassified_genomes(self, target_genomes: List[str],
                                    related_genomes: List[str],
                                    misclassified_genomes: List[Dict[str, Any]],
                                    genome_names: List[str]) -> Tuple[List[str], List[str]]:
        """Filter out misclassified genomes from the genome lists."""

        misclassified_names = {g['genome'] for g in misclassified_genomes}

        # Filter target genomes
        validated_target = []
        for genome_file in target_genomes:
            genome_name = Path(genome_file).stem
            if genome_name not in misclassified_names:
                validated_target.append(genome_file)

        # Filter related genomes
        validated_related = []
        for genome_file in related_genomes:
            genome_name = Path(genome_file).stem
            if genome_name not in misclassified_names:
                validated_related.append(genome_file)

        removed_count = len(target_genomes) + len(related_genomes) - len(validated_target) - len(validated_related)
        if removed_count > 0:
            self.logger.info(f"Removed {removed_count} misclassified genomes")

        return validated_target, validated_related

    def _create_empty_result(self) -> Dict[str, Any]:
        """Create empty result when insufficient genomes."""
        return {
            "validated_target_genomes": [],
            "validated_related_genomes": [],
            "ani_matrix": [],
            "genome_names": [],
            "taxonomic_analysis": {},
            "clustering_results": {},
            "misclassified_genomes": [],
            "ani_report": {"summary": {"total_genomes": 0}}
        }

    def get_output_files(self) -> List[str]:
        """Get list of output files."""
        return [
            str(self.create_stage_file("ani_matrix.csv")),
            str(self.create_stage_file("ani_report.json")),
            str(self.create_stage_file("ani_analysis_plots.png"))
        ]
