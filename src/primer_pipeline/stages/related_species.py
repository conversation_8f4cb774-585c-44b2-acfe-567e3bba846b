"""
Stage 2: Related Species Collection

Collects genomes from related species (same genus) for comparative analysis
and primer specificity validation.
"""

import json
import pandas as pd
from pathlib import Path
from typing import Any, Dict, List, Optional
import subprocess
import shutil
import re

from .base_stage import BaseStage


class RelatedSpeciesStage(BaseStage):
    """Stage 2: Collect genomes from related species for comparison."""
    
    def validate_input(self, input_data: Any) -> bool:
        """Validate input data contains target genome information."""
        return (isinstance(input_data, dict) and 
                'species_name' in input_data and
                'target_genomes' in input_data)
    
    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Collect genomes from related species."""
        species_name = input_data['species_name']
        config = self.get_config_section('related_species')
        
        self.logger.info(f"Collecting related species genomes for: {species_name}")
        
        # Step 1: Extract genus from species name
        genus = self._extract_genus(species_name)
        self.logger.info(f"Target genus: {genus}")
        
        # Step 2: Search for species in the same genus
        related_species = self._find_related_species(genus, species_name, config)
        
        if not related_species:
            self.logger.warning(f"No related species found for genus: {genus}")
            return {
                "related_genomes": [],
                "related_species_metadata": [],
                "total_related_genomes": 0,
                "related_species_summary": {"species_count": 0}
            }
        
        self.logger.info(f"Found {len(related_species)} related species")
        
        # Step 3: Download genomes for related species
        download_dir = self.stage_dir / "related_genomes"
        download_dir.mkdir(exist_ok=True)
        
        downloaded_genomes, species_metadata = self._download_related_genomes(
            related_species, download_dir, config
        )
        
        # Step 4: Create taxonomic relationship documentation
        taxonomy_info = self._document_taxonomic_relationships(
            species_name, related_species, species_metadata
        )
        
        # Step 5: Save metadata and create summary
        metadata_file = self.create_stage_file("related_species_metadata.csv")
        if species_metadata:
            pd.DataFrame(species_metadata).to_csv(metadata_file, index=False)
        
        summary = self._create_summary(related_species, downloaded_genomes, taxonomy_info)
        self.save_stage_report(summary, "related_species_summary.json")
        
        return {
            "related_genomes": downloaded_genomes,
            "related_species_metadata": species_metadata,
            "total_related_genomes": len(downloaded_genomes),
            "taxonomy_info": taxonomy_info,
            "related_species_summary": summary
        }
    
    def _extract_genus(self, species_name: str) -> str:
        """Extract genus from species name."""
        # Species name format: "Genus species"
        parts = species_name.strip().split()
        if len(parts) >= 2:
            return parts[0]
        else:
            raise ValueError(f"Invalid species name format: {species_name}")
    
    def _find_related_species(self, genus: str, target_species: str, 
                            config: Dict[str, Any]) -> List[str]:
        """Find species in the same genus using NCBI taxonomy."""
        self.logger.info(f"Searching for species in genus: {genus}")
        
        try:
            # Search for all species in the genus
            cmd = [
                'datasets', 'summary', 'genome', 'taxon', f'"{genus}"',
                '--as-json-lines'
            ]
            
            result = subprocess.run(
                ' '.join(cmd), shell=True, capture_output=True, text=True, check=True
            )
            
            # Parse results and extract unique species
            species_set = set()
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        genome_data = json.loads(line)
                        organism_name = genome_data.get('organism', {}).get('organism_name', '')
                        
                        # Extract species name (first two words)
                        species_parts = organism_name.split()
                        if len(species_parts) >= 2:
                            species = f"{species_parts[0]} {species_parts[1]}"
                            if species != target_species and species.startswith(genus):
                                species_set.add(species)
                    except json.JSONDecodeError:
                        continue
            
            related_species = list(species_set)
            
            # Limit number of species
            max_species = config.get('max_species_per_genus', 20)
            if len(related_species) > max_species:
                related_species = related_species[:max_species]
                self.logger.info(f"Limited to {max_species} related species")
            
            return related_species
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to search related species: {e.stderr}")
            return []
    
    def _download_related_genomes(self, related_species: List[str], 
                                download_dir: Path, config: Dict[str, Any]) -> tuple:
        """Download genomes for related species."""
        self.logger.info(f"Downloading genomes for {len(related_species)} related species...")
        
        downloaded_genomes = []
        species_metadata = []
        
        max_genomes_per_species = config.get('max_genomes_per_species', 10)
        min_genomes_per_species = config.get('min_genomes_per_species', 1)
        
        for i, species in enumerate(related_species):
            self.log_progress(i + 1, len(related_species), f"Processing {species}")
            
            try:
                # Search genomes for this species
                species_genomes = self._search_species_genomes(species)
                
                if len(species_genomes) < min_genomes_per_species:
                    self.logger.warning(f"Skipping {species}: only {len(species_genomes)} genomes found")
                    continue
                
                # Select top genomes for this species
                selected_genomes = self._select_species_genomes(
                    species_genomes, max_genomes_per_species
                )
                
                # Download genomes
                species_dir = download_dir / species.replace(' ', '_')
                species_dir.mkdir(exist_ok=True)
                
                species_files = self._download_species_genomes(selected_genomes, species_dir)
                
                # Update metadata
                for genome in selected_genomes:
                    genome['species'] = species
                    genome['file_path'] = None
                    
                    # Find corresponding file
                    accession = genome['accession']
                    for file_path in species_files:
                        if accession in str(file_path):
                            genome['file_path'] = str(file_path)
                            break
                
                downloaded_genomes.extend(species_files)
                species_metadata.extend(selected_genomes)
                
                self.logger.info(f"Downloaded {len(species_files)} genomes for {species}")
                
            except Exception as e:
                self.logger.warning(f"Failed to download genomes for {species}: {e}")
                continue
        
        return downloaded_genomes, species_metadata
    
    def _search_species_genomes(self, species_name: str) -> List[Dict[str, Any]]:
        """Search genomes for a specific species."""
        cmd = [
            'datasets', 'summary', 'genome', 'taxon', f'"{species_name}"',
            '--as-json-lines', '--exclude-partial', '--exclude-anomalous'
        ]
        
        try:
            result = subprocess.run(
                ' '.join(cmd), shell=True, capture_output=True, text=True, check=True
            )
            
            genomes = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        genome_data = json.loads(line)
                        
                        # Extract relevant information
                        assembly_info = genome_data.get('assembly_info', {})
                        assembly_stats = assembly_info.get('assembly_stats', {})
                        
                        genome = {
                            'accession': genome_data.get('accession'),
                            'organism_name': genome_data.get('organism', {}).get('organism_name'),
                            'assembly_level': assembly_info.get('assembly_level'),
                            'total_sequence_length': assembly_stats.get('total_sequence_length', 0),
                            'contig_n50': assembly_stats.get('contig_n50', 0),
                            'number_of_contigs': assembly_stats.get('number_of_contigs', 0),
                        }
                        
                        # Calculate quality score
                        genome['quality_score'] = self._calculate_quality_score(genome)
                        genomes.append(genome)
                        
                    except json.JSONDecodeError:
                        continue
            
            return genomes
            
        except subprocess.CalledProcessError:
            return []
    
    def _select_species_genomes(self, genomes: List[Dict[str, Any]], 
                              max_count: int) -> List[Dict[str, Any]]:
        """Select best genomes for a species."""
        if not genomes:
            return []
        
        # Sort by quality score
        sorted_genomes = sorted(genomes, key=lambda x: x['quality_score'], reverse=True)
        
        return sorted_genomes[:max_count]
    
    def _calculate_quality_score(self, genome: Dict[str, Any]) -> float:
        """Calculate quality score for genome ranking."""
        score = 0.0
        
        # Assembly level scoring
        level_scores = {
            'Complete Genome': 100,
            'Chromosome': 80,
            'Scaffold': 60,
            'Contig': 40
        }
        score += level_scores.get(genome.get('assembly_level'), 0)
        
        # N50 scoring
        n50 = genome.get('contig_n50', 0)
        if n50 > 0:
            score += min(50, n50 / 100000)
        
        # Contig count scoring
        contig_count = genome.get('number_of_contigs', float('inf'))
        if contig_count <= 10:
            score += 30
        elif contig_count <= 100:
            score += 20
        elif contig_count <= 1000:
            score += 10
        
        return score
    
    def _download_species_genomes(self, genomes: List[Dict[str, Any]], 
                                species_dir: Path) -> List[str]:
        """Download genomes for a specific species."""
        if not genomes:
            return []
        
        accessions = [g['accession'] for g in genomes]
        
        # Create accession file
        accession_file = species_dir / "accessions.txt"
        with open(accession_file, 'w') as f:
            for acc in accessions:
                f.write(f"{acc}\n")
        
        # Download
        cmd = [
            'datasets', 'download', 'genome', 'accession',
            '--inputfile', str(accession_file),
            '--filename', str(species_dir / 'genomes.zip'),
            '--include', 'genome'
        ]
        
        try:
            subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Extract files
            downloaded_files = []
            zip_file = species_dir / 'genomes.zip'
            
            if zip_file.exists():
                import zipfile
                with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                    zip_ref.extractall(species_dir)
                
                # Find and rename genome files
                for genome_dir in (species_dir / 'ncbi_dataset' / 'data').glob('*'):
                    if genome_dir.is_dir():
                        for fasta_file in genome_dir.glob('*.fna'):
                            new_name = species_dir / f"{genome_dir.name}.fna"
                            shutil.copy2(fasta_file, new_name)
                            downloaded_files.append(str(new_name))
                
                # Cleanup
                zip_file.unlink()
                shutil.rmtree(species_dir / 'ncbi_dataset', ignore_errors=True)
                accession_file.unlink()
            
            return downloaded_files
            
        except subprocess.CalledProcessError:
            return []
    
    def _document_taxonomic_relationships(self, target_species: str, 
                                        related_species: List[str],
                                        metadata: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Document taxonomic relationships."""
        genus = self._extract_genus(target_species)
        
        # Count genomes per species
        species_counts = {}
        for genome in metadata:
            species = genome.get('species', 'Unknown')
            species_counts[species] = species_counts.get(species, 0) + 1
        
        return {
            "target_species": target_species,
            "genus": genus,
            "related_species_count": len(related_species),
            "related_species_list": related_species,
            "genomes_per_species": species_counts,
            "total_related_genomes": len(metadata)
        }
    
    def _create_summary(self, related_species: List[str], downloaded_genomes: List[str],
                       taxonomy_info: Dict[str, Any]) -> Dict[str, Any]:
        """Create related species collection summary."""
        return {
            "species_count": len(related_species),
            "total_genomes_downloaded": len(downloaded_genomes),
            "genomes_per_species": taxonomy_info.get("genomes_per_species", {}),
            "taxonomy_info": taxonomy_info
        }
    
    def get_output_files(self) -> List[str]:
        """Get list of output files."""
        return [
            str(self.create_stage_file("related_species_metadata.csv")),
            str(self.create_stage_file("related_species_summary.json")),
            str(self.stage_dir / "related_genomes")
        ]
