"""
Stage 5: Species-Specific K-mer Analysis

Identifies k-mers that are specific to the target species by comparing
k-mer frequencies between target and related species genomes.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple
import subprocess
import tempfile
import json
from collections import defaultdict, Counter
from Bio import SeqIO
import matplotlib.pyplot as plt
import seaborn as sns

from .base_stage import BaseStage


class KmerAnalysisStage(BaseStage):
    """Stage 5: Identify species-specific k-mers for primer design."""
    
    def validate_input(self, input_data: Any) -> bool:
        """Validate input data contains validated genomes."""
        return (isinstance(input_data, dict) and 
                'validated_target_genomes' in input_data and
                'validated_related_genomes' in input_data)
    
    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform k-mer analysis to identify species-specific sequences."""
        target_genomes = input_data['validated_target_genomes']
        related_genomes = input_data['validated_related_genomes']
        config = self.get_config_section('kmer_analysis')
        
        self.logger.info("Starting species-specific k-mer analysis...")
        
        if not target_genomes:
            raise ValueError("No target genomes available for k-mer analysis")
        
        k = config.get('kmer_size', 21)
        self.logger.info(f"Using k-mer size: {k}")
        
        # Step 1: Extract k-mers from target genomes
        target_kmers = self._extract_kmers_from_genomes(target_genomes, k, "target")
        
        # Step 2: Extract k-mers from related genomes
        related_kmers = self._extract_kmers_from_genomes(related_genomes, k, "related") if related_genomes else {}
        
        # Step 3: Identify species-specific k-mers
        specific_kmers = self._identify_specific_kmers(target_kmers, related_kmers, config)
        
        # Step 4: Filter k-mers by frequency and specificity
        filtered_kmers = self._filter_kmers(specific_kmers, target_kmers, related_kmers, config)
        
        # Step 5: Analyze k-mer distribution across genomes
        kmer_distribution = self._analyze_kmer_distribution(filtered_kmers, target_genomes, k)
        
        # Step 6: Generate k-mer analysis plots
        self._generate_kmer_plots(target_kmers, related_kmers, filtered_kmers)
        
        # Step 7: Create comprehensive report
        kmer_report = self._create_kmer_report(
            target_kmers, related_kmers, filtered_kmers, kmer_distribution, config
        )
        
        # Step 8: Save results
        self._save_kmer_results(filtered_kmers, kmer_distribution, kmer_report)
        
        return {
            "species_specific_kmers": list(filtered_kmers.keys()),
            "kmer_frequencies": filtered_kmers,
            "kmer_distribution": kmer_distribution,
            "total_specific_kmers": len(filtered_kmers),
            "kmer_report": kmer_report
        }
    
    def _extract_kmers_from_genomes(self, genome_files: List[str], k: int, 
                                  genome_type: str) -> Dict[str, int]:
        """Extract k-mers from a set of genomes using Jellyfish."""
        self.logger.info(f"Extracting {k}-mers from {len(genome_files)} {genome_type} genomes...")
        
        # Check if Jellyfish is available
        self.check_required_tools(['jellyfish'])
        
        config = self.get_config_section('kmer_analysis')
        hash_size = config.get('jellyfish_hash_size', '1G')
        canonical = config.get('canonical_kmers', True)
        
        all_kmers = Counter()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            for i, genome_file in enumerate(genome_files):
                self.log_progress(i + 1, len(genome_files), 
                                f"Processing {Path(genome_file).name}")
                
                try:
                    # Count k-mers with Jellyfish
                    jf_file = temp_path / f"kmers_{i}.jf"
                    
                    cmd = ['jellyfish', 'count', '-m', str(k), '-s', hash_size, 
                          '-o', str(jf_file)]
                    
                    if canonical:
                        cmd.append('-C')
                    
                    cmd.append(genome_file)
                    
                    subprocess.run(cmd, check=True, capture_output=True)
                    
                    # Dump k-mers to text
                    dump_file = temp_path / f"kmers_{i}.txt"
                    cmd = ['jellyfish', 'dump', str(jf_file)]
                    
                    with open(dump_file, 'w') as f:
                        subprocess.run(cmd, stdout=f, check=True)
                    
                    # Parse k-mer counts
                    genome_kmers = self._parse_jellyfish_output(dump_file)
                    
                    # Add to total counts
                    for kmer, count in genome_kmers.items():
                        all_kmers[kmer] += count
                    
                except subprocess.CalledProcessError as e:
                    self.logger.warning(f"Failed to process {genome_file}: {e}")
                    continue
        
        self.logger.info(f"Extracted {len(all_kmers)} unique {k}-mers from {genome_type} genomes")
        return dict(all_kmers)
    
    def _parse_jellyfish_output(self, dump_file: Path) -> Dict[str, int]:
        """Parse Jellyfish dump output."""
        kmers = {}
        
        with open(dump_file, 'r') as f:
            lines = f.readlines()
        
        i = 0
        while i < len(lines):
            if lines[i].startswith('>'):
                # Count line
                count = int(lines[i][1:].strip())
                # Sequence line
                if i + 1 < len(lines):
                    kmer = lines[i + 1].strip().upper()
                    kmers[kmer] = count
                i += 2
            else:
                i += 1
        
        return kmers
    
    def _identify_specific_kmers(self, target_kmers: Dict[str, int], 
                               related_kmers: Dict[str, int],
                               config: Dict[str, Any]) -> Dict[str, int]:
        """Identify k-mers specific to target species."""
        self.logger.info("Identifying species-specific k-mers...")
        
        max_related_freq = config.get('max_kmer_frequency_related', 0)
        
        specific_kmers = {}
        
        for kmer, target_count in target_kmers.items():
            related_count = related_kmers.get(kmer, 0)
            
            # K-mer is specific if it's absent or very rare in related species
            if related_count <= max_related_freq:
                specific_kmers[kmer] = target_count
        
        self.logger.info(f"Identified {len(specific_kmers)} species-specific k-mers")
        return specific_kmers
    
    def _filter_kmers(self, specific_kmers: Dict[str, int], 
                     target_kmers: Dict[str, int], related_kmers: Dict[str, int],
                     config: Dict[str, Any]) -> Dict[str, int]:
        """Filter k-mers by frequency and specificity thresholds."""
        self.logger.info("Filtering k-mers by frequency and specificity...")
        
        min_frequency = config.get('min_kmer_frequency', 5)
        specificity_threshold = config.get('specificity_threshold', 0.95)
        
        filtered_kmers = {}
        
        for kmer, target_count in specific_kmers.items():
            # Check minimum frequency
            if target_count < min_frequency:
                continue
            
            # Calculate specificity score
            related_count = related_kmers.get(kmer, 0)
            total_count = target_count + related_count
            
            if total_count > 0:
                specificity = target_count / total_count
                
                if specificity >= specificity_threshold:
                    filtered_kmers[kmer] = target_count
        
        self.logger.info(f"Filtered to {len(filtered_kmers)} high-quality specific k-mers")
        return filtered_kmers
    
    def _analyze_kmer_distribution(self, specific_kmers: Dict[str, int], 
                                 target_genomes: List[str], k: int) -> Dict[str, Any]:
        """Analyze distribution of specific k-mers across target genomes."""
        self.logger.info("Analyzing k-mer distribution across target genomes...")
        
        # Count k-mers in each genome
        genome_kmer_counts = {}
        
        for i, genome_file in enumerate(target_genomes):
            self.log_progress(i + 1, len(target_genomes), 
                            f"Analyzing {Path(genome_file).name}")
            
            genome_name = Path(genome_file).stem
            genome_kmers = set()
            
            # Extract k-mers from this genome
            for record in SeqIO.parse(genome_file, "fasta"):
                sequence = str(record.seq).upper()
                for j in range(len(sequence) - k + 1):
                    kmer = sequence[j:j+k]
                    if kmer in specific_kmers:
                        genome_kmers.add(kmer)
            
            genome_kmer_counts[genome_name] = len(genome_kmers)
        
        # Calculate conservation statistics
        kmer_genome_presence = defaultdict(int)
        
        for genome_file in target_genomes:
            genome_kmers = set()
            
            for record in SeqIO.parse(genome_file, "fasta"):
                sequence = str(record.seq).upper()
                for j in range(len(sequence) - k + 1):
                    kmer = sequence[j:j+k]
                    if kmer in specific_kmers:
                        genome_kmers.add(kmer)
            
            for kmer in genome_kmers:
                kmer_genome_presence[kmer] += 1
        
        # Calculate conservation levels
        total_genomes = len(target_genomes)
        conservation_levels = {
            "highly_conserved": 0,  # Present in >80% of genomes
            "moderately_conserved": 0,  # Present in 50-80% of genomes
            "poorly_conserved": 0  # Present in <50% of genomes
        }
        
        for kmer, presence_count in kmer_genome_presence.items():
            conservation_ratio = presence_count / total_genomes
            
            if conservation_ratio > 0.8:
                conservation_levels["highly_conserved"] += 1
            elif conservation_ratio > 0.5:
                conservation_levels["moderately_conserved"] += 1
            else:
                conservation_levels["poorly_conserved"] += 1
        
        return {
            "genome_kmer_counts": genome_kmer_counts,
            "kmer_genome_presence": dict(kmer_genome_presence),
            "conservation_levels": conservation_levels,
            "total_target_genomes": total_genomes
        }
    
    def _generate_kmer_plots(self, target_kmers: Dict[str, int], 
                           related_kmers: Dict[str, int],
                           filtered_kmers: Dict[str, int]) -> None:
        """Generate k-mer analysis visualizations."""
        self.logger.info("Generating k-mer analysis plots...")
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('K-mer Analysis Results', fontsize=16)
        
        # Plot 1: K-mer frequency distribution
        ax = axes[0, 0]
        
        target_freqs = list(target_kmers.values())
        related_freqs = list(related_kmers.values()) if related_kmers else []
        
        ax.hist(target_freqs, bins=50, alpha=0.7, label='Target k-mers', density=True)
        if related_freqs:
            ax.hist(related_freqs, bins=50, alpha=0.7, label='Related k-mers', density=True)
        
        ax.set_xlabel('K-mer Frequency')
        ax.set_ylabel('Density')
        ax.set_title('K-mer Frequency Distribution')
        ax.set_yscale('log')
        ax.legend()
        
        # Plot 2: Specific k-mer frequencies
        ax = axes[0, 1]
        
        specific_freqs = list(filtered_kmers.values())
        if specific_freqs:
            ax.hist(specific_freqs, bins=30, alpha=0.7, edgecolor='black')
            ax.set_xlabel('K-mer Frequency')
            ax.set_ylabel('Count')
            ax.set_title('Species-Specific K-mer Frequencies')
        
        # Plot 3: K-mer counts comparison
        ax = axes[1, 0]
        
        categories = ['Total Target', 'Total Related', 'Species-Specific']
        counts = [len(target_kmers), len(related_kmers), len(filtered_kmers)]
        
        bars = ax.bar(categories, counts, color=['blue', 'orange', 'green'])
        ax.set_ylabel('Number of K-mers')
        ax.set_title('K-mer Count Comparison')
        
        # Add value labels on bars
        for bar, count in zip(bars, counts):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{count:,}', ha='center', va='bottom')
        
        # Plot 4: Specificity distribution
        ax = axes[1, 1]
        
        if related_kmers:
            specificities = []
            for kmer in filtered_kmers:
                target_count = target_kmers.get(kmer, 0)
                related_count = related_kmers.get(kmer, 0)
                total_count = target_count + related_count
                
                if total_count > 0:
                    specificity = target_count / total_count
                    specificities.append(specificity)
            
            if specificities:
                ax.hist(specificities, bins=20, alpha=0.7, edgecolor='black')
                ax.set_xlabel('Specificity Score')
                ax.set_ylabel('Count')
                ax.set_title('K-mer Specificity Distribution')
                ax.axvline(0.95, color='red', linestyle='--', label='Threshold')
                ax.legend()
        
        plt.tight_layout()
        
        # Save plot
        plot_file = self.create_stage_file("kmer_analysis_plots.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"K-mer plots saved: {plot_file}")
    
    def _create_kmer_report(self, target_kmers: Dict[str, int], 
                          related_kmers: Dict[str, int],
                          filtered_kmers: Dict[str, int],
                          kmer_distribution: Dict[str, Any],
                          config: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive k-mer analysis report."""
        
        return {
            "parameters": {
                "kmer_size": config.get('kmer_size', 21),
                "min_frequency": config.get('min_kmer_frequency', 5),
                "max_related_frequency": config.get('max_kmer_frequency_related', 0),
                "specificity_threshold": config.get('specificity_threshold', 0.95)
            },
            "summary": {
                "total_target_kmers": len(target_kmers),
                "total_related_kmers": len(related_kmers),
                "species_specific_kmers": len(filtered_kmers),
                "specificity_rate": len(filtered_kmers) / len(target_kmers) if target_kmers else 0
            },
            "frequency_statistics": {
                "target_kmers": self._calculate_frequency_stats(list(target_kmers.values())),
                "related_kmers": self._calculate_frequency_stats(list(related_kmers.values())) if related_kmers else {},
                "specific_kmers": self._calculate_frequency_stats(list(filtered_kmers.values()))
            },
            "conservation_analysis": kmer_distribution["conservation_levels"],
            "genome_distribution": kmer_distribution["genome_kmer_counts"]
        }
    
    def _calculate_frequency_stats(self, frequencies: List[int]) -> Dict[str, float]:
        """Calculate frequency statistics."""
        if not frequencies:
            return {}
        
        return {
            "count": len(frequencies),
            "mean": float(np.mean(frequencies)),
            "median": float(np.median(frequencies)),
            "std": float(np.std(frequencies)),
            "min": int(np.min(frequencies)),
            "max": int(np.max(frequencies)),
            "q25": float(np.percentile(frequencies, 25)),
            "q75": float(np.percentile(frequencies, 75))
        }
    
    def _save_kmer_results(self, filtered_kmers: Dict[str, int], 
                         kmer_distribution: Dict[str, Any],
                         kmer_report: Dict[str, Any]) -> None:
        """Save k-mer analysis results."""
        
        # Save specific k-mers
        kmers_file = self.create_stage_file("species_specific_kmers.txt")
        with open(kmers_file, 'w') as f:
            for kmer, frequency in sorted(filtered_kmers.items(), key=lambda x: x[1], reverse=True):
                f.write(f"{kmer}\t{frequency}\n")
        
        # Save k-mer distribution
        distribution_file = self.create_stage_file("kmer_distribution.json")
        with open(distribution_file, 'w') as f:
            json.dump(kmer_distribution, f, indent=2)
        
        # Save comprehensive report
        self.save_stage_report(kmer_report, "kmer_report.json")
        
        self.logger.info("K-mer analysis results saved")
    
    def get_output_files(self) -> List[str]:
        """Get list of output files."""
        return [
            str(self.create_stage_file("species_specific_kmers.txt")),
            str(self.create_stage_file("kmer_distribution.json")),
            str(self.create_stage_file("kmer_report.json")),
            str(self.create_stage_file("kmer_analysis_plots.png"))
        ]
