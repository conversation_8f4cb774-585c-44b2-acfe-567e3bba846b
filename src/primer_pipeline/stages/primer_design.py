"""
Stage 7: Primer Design

Designs PCR primers in the identified species-specific genomic regions
and validates their specificity against related species genomes.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Any, Dict, List, Optional, NamedTuple
from Bio import SeqIO
from Bio.SeqUtils import MeltingTemp as mt
try:
    from Bio.SeqUtils import gc_fraction as GC
except ImportError:
    from Bio.SeqUtils import GC
import primer3
import subprocess
import tempfile
import matplotlib.pyplot as plt
import seaborn as sns

from .base_stage import BaseStage


class PrimerPair(NamedTuple):
    """Represents a primer pair with all relevant information."""
    region_id: str
    forward_seq: str
    reverse_seq: str
    forward_tm: float
    reverse_tm: float
    forward_gc: float
    reverse_gc: float
    product_size: int
    specificity_score: float
    target_hits: int
    related_hits: int
    primer3_score: float


class PrimerDesignStage(BaseStage):
    """Stage 7: Design and validate species-specific PCR primers."""
    
    def validate_input(self, input_data: Any) -> bool:
        """Validate input data contains candidate regions."""
        return (isinstance(input_data, dict) and 
                'candidate_regions' in input_data and
                'validated_target_genomes' in input_data)
    
    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Design and validate PCR primers."""
        candidate_regions = input_data['candidate_regions']
        target_genomes = input_data['validated_target_genomes']
        related_genomes = input_data.get('validated_related_genomes', [])
        config = self.get_config_section('primer_design')
        
        self.logger.info("Starting primer design...")
        
        if not candidate_regions:
            raise ValueError("No candidate regions available for primer design")
        
        self.logger.info(f"Designing primers for {len(candidate_regions)} candidate regions")
        
        # Step 1: Design primers for each region using Primer3
        all_primer_pairs = []
        
        for i, region in enumerate(candidate_regions):
            self.log_progress(i + 1, len(candidate_regions), 
                            f"Designing primers for region {i+1}")
            
            region_primers = self._design_primers_for_region(region, config)
            all_primer_pairs.extend(region_primers)
        
        self.logger.info(f"Generated {len(all_primer_pairs)} initial primer pairs")
        
        # Step 2: Validate primer specificity
        validated_primers = self._validate_primer_specificity(
            all_primer_pairs, target_genomes, related_genomes, config
        )
        
        # Step 3: Rank and select best primers
        ranked_primers = self._rank_primer_pairs(validated_primers, config)
        
        # Step 4: Generate primer analysis plots
        self._generate_primer_plots(ranked_primers)
        
        # Step 5: Create comprehensive report
        primer_report = self._create_primer_report(ranked_primers, config)
        
        # Step 6: Save results
        self._save_primer_results(ranked_primers, primer_report)
        
        return {
            "primer_pairs": [primer._asdict() for primer in ranked_primers],
            "total_primer_pairs": len(ranked_primers),
            "primer_report": primer_report
        }
    
    def _design_primers_for_region(self, region: Dict[str, Any], 
                                 config: Dict[str, Any]) -> List[PrimerPair]:
        """Design primers for a single genomic region using Primer3."""
        
        sequence = region['sequence']
        region_id = f"region_{region.get('contig', 'unknown')}_{region.get('start', 0)}"
        
        # Primer3 parameters
        primer3_params = {
            'SEQUENCE_ID': region_id,
            'SEQUENCE_TEMPLATE': sequence,
            'PRIMER_OPT_SIZE': config.get('primer_opt_size', 20),
            'PRIMER_MIN_SIZE': config.get('primer_min_size', 18),
            'PRIMER_MAX_SIZE': config.get('primer_max_size', 25),
            'PRIMER_OPT_TM': config.get('primer_opt_tm', 60.0),
            'PRIMER_MIN_TM': config.get('primer_min_tm', 57.0),
            'PRIMER_MAX_TM': config.get('primer_max_tm', 63.0),
            'PRIMER_OPT_GC_PERCENT': config.get('primer_opt_gc_percent', 50.0),
            'PRIMER_MIN_GC': config.get('primer_min_gc', 40.0),
            'PRIMER_MAX_GC': config.get('primer_max_gc', 60.0),
            'PRIMER_MAX_POLY_X': config.get('primer_max_poly_x', 4),
            'PRIMER_GC_CLAMP': config.get('primer_gc_clamp', 1),
            'PRIMER_PRODUCT_SIZE_RANGE': config.get('product_size_range', [100, 300]),
            'PRIMER_NUM_RETURN': 10  # Return multiple primer pairs per region
        }
        
        try:
            # Run Primer3
            primer3_result = primer3.bindings.designPrimers(primer3_params)
            
            primer_pairs = []
            num_pairs = primer3_result.get('PRIMER_PAIR_NUM_RETURNED', 0)
            
            for i in range(num_pairs):
                # Extract primer sequences
                forward_seq = primer3_result.get(f'PRIMER_LEFT_{i}_SEQUENCE', '')
                reverse_seq = primer3_result.get(f'PRIMER_RIGHT_{i}_SEQUENCE', '')
                
                if not forward_seq or not reverse_seq:
                    continue
                
                # Calculate properties
                forward_tm = primer3_result.get(f'PRIMER_LEFT_{i}_TM', 0)
                reverse_tm = primer3_result.get(f'PRIMER_RIGHT_{i}_TM', 0)
                try:
                    forward_gc = GC(forward_seq) * 100  # gc_fraction returns 0-1
                    reverse_gc = GC(reverse_seq) * 100
                except:
                    forward_gc = GC(forward_seq)  # Old GC function returns percentage
                    reverse_gc = GC(reverse_seq)
                product_size = primer3_result.get(f'PRIMER_PAIR_{i}_PRODUCT_SIZE', 0)
                primer3_score = primer3_result.get(f'PRIMER_PAIR_{i}_PENALTY', float('inf'))
                
                primer_pair = PrimerPair(
                    region_id=region_id,
                    forward_seq=forward_seq,
                    reverse_seq=reverse_seq,
                    forward_tm=forward_tm,
                    reverse_tm=reverse_tm,
                    forward_gc=forward_gc,
                    reverse_gc=reverse_gc,
                    product_size=product_size,
                    specificity_score=0.0,  # Will be calculated later
                    target_hits=0,  # Will be calculated later
                    related_hits=0,  # Will be calculated later
                    primer3_score=primer3_score
                )
                
                primer_pairs.append(primer_pair)
            
            return primer_pairs
            
        except Exception as e:
            self.logger.warning(f"Failed to design primers for region {region_id}: {e}")
            return []
    
    def _validate_primer_specificity(self, primer_pairs: List[PrimerPair],
                                   target_genomes: List[str], related_genomes: List[str],
                                   config: Dict[str, Any]) -> List[PrimerPair]:
        """Validate primer specificity against target and related genomes."""
        self.logger.info("Validating primer specificity...")
        
        max_mismatches = config.get('max_mismatches', 2)
        max_related_hits = config.get('max_primer_hits_related', 0)
        
        validated_primers = []
        
        for i, primer_pair in enumerate(primer_pairs):
            self.log_progress(i + 1, len(primer_pairs), "Validating primer specificity")
            
            # Count hits in target genomes
            target_hits = self._count_primer_hits(
                primer_pair, target_genomes, max_mismatches
            )
            
            # Count hits in related genomes
            related_hits = self._count_primer_hits(
                primer_pair, related_genomes, max_mismatches
            ) if related_genomes else 0
            
            # Calculate specificity score
            total_hits = target_hits + related_hits
            specificity_score = target_hits / total_hits if total_hits > 0 else 0.0
            
            # Filter by specificity criteria
            if related_hits <= max_related_hits and target_hits > 0:
                # Create updated primer pair with specificity information
                updated_primer = primer_pair._replace(
                    target_hits=target_hits,
                    related_hits=related_hits,
                    specificity_score=specificity_score
                )
                validated_primers.append(updated_primer)
        
        self.logger.info(f"Validated {len(validated_primers)} specific primer pairs")
        return validated_primers
    
    def _count_primer_hits(self, primer_pair: PrimerPair, genome_files: List[str],
                         max_mismatches: int) -> int:
        """Count primer hits in a set of genomes allowing for mismatches."""
        
        if not genome_files:
            return 0
        
        total_hits = 0
        
        for genome_file in genome_files:
            try:
                # Use simple string matching for exact matches
                # For more sophisticated matching with mismatches, 
                # you could use tools like BLAST or implement fuzzy matching
                
                for record in SeqIO.parse(genome_file, "fasta"):
                    sequence = str(record.seq).upper()
                    
                    # Count exact matches (simplified approach)
                    forward_hits = sequence.count(primer_pair.forward_seq.upper())
                    reverse_hits = sequence.count(primer_pair.reverse_seq.upper())
                    
                    # Also check reverse complement
                    from Bio.Seq import Seq
                    forward_rc = str(Seq(primer_pair.forward_seq).reverse_complement())
                    reverse_rc = str(Seq(primer_pair.reverse_seq).reverse_complement())
                    
                    forward_hits += sequence.count(forward_rc.upper())
                    reverse_hits += sequence.count(reverse_rc.upper())
                    
                    total_hits += forward_hits + reverse_hits
                    
            except Exception as e:
                self.logger.warning(f"Error processing {genome_file}: {e}")
                continue
        
        return total_hits
    
    def _rank_primer_pairs(self, primer_pairs: List[PrimerPair], 
                         config: Dict[str, Any]) -> List[PrimerPair]:
        """Rank primer pairs by quality metrics."""
        self.logger.info("Ranking primer pairs...")
        
        rank_by = config.get('rank_by', 'specificity')
        max_primers = config.get('max_primer_pairs', 10)
        
        if rank_by == 'specificity':
            # Rank by specificity score (descending)
            ranked_primers = sorted(primer_pairs, 
                                  key=lambda x: x.specificity_score, reverse=True)
        elif rank_by == 'tm_difference':
            # Rank by minimal Tm difference between forward and reverse
            ranked_primers = sorted(primer_pairs, 
                                  key=lambda x: abs(x.forward_tm - x.reverse_tm))
        elif rank_by == 'gc_content':
            # Rank by GC content closest to optimal (50%)
            ranked_primers = sorted(primer_pairs, 
                                  key=lambda x: abs((x.forward_gc + x.reverse_gc) / 2 - 50))
        else:
            # Default: rank by Primer3 score (lower is better)
            ranked_primers = sorted(primer_pairs, 
                                  key=lambda x: x.primer3_score)
        
        # Limit to maximum number of primers
        final_primers = ranked_primers[:max_primers]
        
        self.logger.info(f"Selected top {len(final_primers)} primer pairs")
        return final_primers
    
    def _generate_primer_plots(self, primer_pairs: List[PrimerPair]) -> None:
        """Generate primer analysis visualizations."""
        self.logger.info("Generating primer analysis plots...")
        
        if not primer_pairs:
            self.logger.warning("No primer pairs to plot")
            return
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Primer Design Analysis', fontsize=16)
        
        # Plot 1: Tm distribution
        ax = axes[0, 0]
        forward_tms = [p.forward_tm for p in primer_pairs]
        reverse_tms = [p.reverse_tm for p in primer_pairs]
        
        ax.hist(forward_tms, alpha=0.7, label='Forward primers', bins=15)
        ax.hist(reverse_tms, alpha=0.7, label='Reverse primers', bins=15)
        ax.set_xlabel('Melting Temperature (°C)')
        ax.set_ylabel('Count')
        ax.set_title('Primer Tm Distribution')
        ax.legend()
        
        # Plot 2: GC content distribution
        ax = axes[0, 1]
        forward_gcs = [p.forward_gc for p in primer_pairs]
        reverse_gcs = [p.reverse_gc for p in primer_pairs]
        
        ax.hist(forward_gcs, alpha=0.7, label='Forward primers', bins=15)
        ax.hist(reverse_gcs, alpha=0.7, label='Reverse primers', bins=15)
        ax.set_xlabel('GC Content (%)')
        ax.set_ylabel('Count')
        ax.set_title('Primer GC Content Distribution')
        ax.legend()
        
        # Plot 3: Product size distribution
        ax = axes[1, 0]
        product_sizes = [p.product_size for p in primer_pairs]
        
        ax.hist(product_sizes, bins=15, alpha=0.7, edgecolor='black')
        ax.set_xlabel('Product Size (bp)')
        ax.set_ylabel('Count')
        ax.set_title('PCR Product Size Distribution')
        
        # Plot 4: Specificity scores
        ax = axes[1, 1]
        specificity_scores = [p.specificity_score for p in primer_pairs]
        
        ax.hist(specificity_scores, bins=15, alpha=0.7, edgecolor='black')
        ax.set_xlabel('Specificity Score')
        ax.set_ylabel('Count')
        ax.set_title('Primer Specificity Distribution')
        
        plt.tight_layout()
        
        # Save plot
        plot_file = self.create_stage_file("primer_analysis_plots.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"Primer plots saved: {plot_file}")
    
    def _create_primer_report(self, primer_pairs: List[PrimerPair],
                            config: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive primer design report."""
        
        if not primer_pairs:
            return {
                "summary": {"total_primer_pairs": 0},
                "parameters": config
            }
        
        # Calculate statistics
        forward_tms = [p.forward_tm for p in primer_pairs]
        reverse_tms = [p.reverse_tm for p in primer_pairs]
        forward_gcs = [p.forward_gc for p in primer_pairs]
        reverse_gcs = [p.reverse_gc for p in primer_pairs]
        product_sizes = [p.product_size for p in primer_pairs]
        specificity_scores = [p.specificity_score for p in primer_pairs]
        
        return {
            "parameters": config,
            "summary": {
                "total_primer_pairs": len(primer_pairs),
                "mean_specificity": float(np.mean(specificity_scores)),
                "mean_product_size": float(np.mean(product_sizes))
            },
            "statistics": {
                "forward_tm": {
                    "mean": float(np.mean(forward_tms)),
                    "std": float(np.std(forward_tms)),
                    "min": float(np.min(forward_tms)),
                    "max": float(np.max(forward_tms))
                },
                "reverse_tm": {
                    "mean": float(np.mean(reverse_tms)),
                    "std": float(np.std(reverse_tms)),
                    "min": float(np.min(reverse_tms)),
                    "max": float(np.max(reverse_tms))
                },
                "forward_gc": {
                    "mean": float(np.mean(forward_gcs)),
                    "std": float(np.std(forward_gcs)),
                    "min": float(np.min(forward_gcs)),
                    "max": float(np.max(forward_gcs))
                },
                "reverse_gc": {
                    "mean": float(np.mean(reverse_gcs)),
                    "std": float(np.std(reverse_gcs)),
                    "min": float(np.min(reverse_gcs)),
                    "max": float(np.max(reverse_gcs))
                },
                "product_size": {
                    "mean": float(np.mean(product_sizes)),
                    "std": float(np.std(product_sizes)),
                    "min": int(np.min(product_sizes)),
                    "max": int(np.max(product_sizes))
                },
                "specificity": {
                    "mean": float(np.mean(specificity_scores)),
                    "std": float(np.std(specificity_scores)),
                    "min": float(np.min(specificity_scores)),
                    "max": float(np.max(specificity_scores))
                }
            },
            "top_primers": [
                {
                    "rank": i + 1,
                    "region_id": primer.region_id,
                    "forward_seq": primer.forward_seq,
                    "reverse_seq": primer.reverse_seq,
                    "forward_tm": primer.forward_tm,
                    "reverse_tm": primer.reverse_tm,
                    "product_size": primer.product_size,
                    "specificity_score": primer.specificity_score,
                    "target_hits": primer.target_hits,
                    "related_hits": primer.related_hits
                }
                for i, primer in enumerate(primer_pairs[:5])  # Top 5 primers
            ]
        }
    
    def _save_primer_results(self, primer_pairs: List[PrimerPair],
                           primer_report: Dict[str, Any]) -> None:
        """Save primer design results."""
        
        # Save primers as CSV
        csv_file = self.create_stage_file("primer_pairs.csv")
        primer_data = []
        
        for i, primer in enumerate(primer_pairs):
            primer_data.append({
                'rank': i + 1,
                'region_id': primer.region_id,
                'forward_seq': primer.forward_seq,
                'reverse_seq': primer.reverse_seq,
                'forward_tm': primer.forward_tm,
                'reverse_tm': primer.reverse_tm,
                'forward_gc': primer.forward_gc,
                'reverse_gc': primer.reverse_gc,
                'product_size': primer.product_size,
                'specificity_score': primer.specificity_score,
                'target_hits': primer.target_hits,
                'related_hits': primer.related_hits,
                'primer3_score': primer.primer3_score
            })
        
        pd.DataFrame(primer_data).to_csv(csv_file, index=False)
        
        # Save comprehensive report
        self.save_stage_report(primer_report, "primer_report.json")
        
        self.logger.info("Primer design results saved")
    
    def get_output_files(self) -> List[str]:
        """Get list of output files."""
        return [
            str(self.create_stage_file("primer_pairs.csv")),
            str(self.create_stage_file("primer_report.json")),
            str(self.create_stage_file("primer_analysis_plots.png"))
        ]
