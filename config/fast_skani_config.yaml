# Fast Configuration using skani for ANI calculations
# Optimized for speed while maintaining good accuracy

# General settings - reduced for faster processing
general:
  max_target_genomes: 50        # Reduced from 100
  max_related_genomes_per_species: 20  # Reduced from 50
  output_dir: "output"
  temp_dir: "temp"
  log_level: "INFO"
  n_threads: 4

# Stage 1: Genome Download & Selection
genome_download:
  ncbi_api_key: null
  assembly_level: ["Complete Genome", "Chromosome", "Scaffold"]  # Exclude contigs for speed
  refseq_category: ["reference genome", "representative genome", "na"]
  exclude_partial: true
  exclude_anomalous: true

# Stage 2: Related Species Collection
related_species:
  taxonomic_rank: "genus"
  max_species_per_genus: 15     # Reduced from 20
  min_genomes_per_species: 1
  max_genomes_per_species: 5    # Reduced from 10

# Stage 3: Quality Assessment & Filtering - relaxed for speed
quality_assessment:
  min_genome_size: 1500000      # Slightly higher minimum
  max_genome_size: 15000000     # Slightly lower maximum
  genome_size_outlier_threshold: 2.5  # More permissive
  gc_content_outlier_threshold: 2.5   # More permissive
  min_n50: 20000                # Higher N50 requirement
  max_contigs: 500              # Fewer contigs allowed
  max_n_content: 0.03           # Stricter N content

# Stage 4: Taxonomic Validation via ANI - FAST skani configuration
ani_analysis:
  ani_method: "skani"           # Use fast skani method
  same_species_threshold: 95.0
  related_species_threshold: 85.0
  # skani specific parameters for speed
  skani_preset: "fast"          # Fastest preset
  skani_min_af: 0.1             # Lower alignment fraction for speed
  clustering_method: "average"

# Stage 5: Species-Specific K-mer Analysis
kmer_analysis:
  kmer_size: 21                 # Standard k-mer size
  min_kmer_frequency: 3         # Reduced from 5 for more k-mers
  max_kmer_frequency_related: 0
  specificity_threshold: 0.9    # Slightly relaxed from 0.95
  jellyfish_hash_size: "500M"   # Smaller hash for speed
  canonical_kmers: true

# Stage 6: Genomic Region Identification
region_identification:
  window_size: 1000
  step_size: 500
  min_specific_kmers_per_window: 8  # Reduced from 10
  min_conservation_across_genomes: 0.7  # Relaxed from 0.8
  region_extension: 150         # Reduced from 200
  max_regions_per_genome: 50    # Reduced from 100

# Stage 7: Primer Design
primer_design:
  # Primer3 settings
  primer_opt_size: 20
  primer_min_size: 18
  primer_max_size: 25
  primer_opt_tm: 60.0
  primer_min_tm: 57.0
  primer_max_tm: 63.0
  primer_opt_gc_percent: 50.0
  primer_min_gc: 40.0
  primer_max_gc: 60.0
  primer_max_poly_x: 4
  primer_gc_clamp: 1
  
  # PCR product settings
  product_size_range: [120, 280]  # Slightly narrower range
  
  # Specificity validation
  max_mismatches: 2
  max_primer_hits_related: 0
  
  # Output settings
  max_primer_pairs: 5           # Reduced from 10 for speed
  rank_by: "specificity"

# Checkpoint settings
checkpoints:
  save_intermediate: true
  compress_checkpoints: true
  cleanup_temp_files: true      # Clean up for space
