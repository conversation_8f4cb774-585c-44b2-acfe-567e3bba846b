# Default configuration for Species-Specific Primer Design Pipeline

# General settings
general:
  max_target_genomes: 100
  max_related_genomes_per_species: 50
  output_dir: "output"
  temp_dir: "temp"
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  n_threads: 4

# Stage 1: Genome Download & Selection
genome_download:
  ncbi_api_key: null  # Optional: NCBI API key for faster downloads
  assembly_level: ["Complete Genome", "Chromosome", "Scaffold", "Contig"]
  refseq_category: ["reference genome", "representative genome", "na"]
  exclude_partial: true
  exclude_anomalous: true

# Stage 2: Related Species Collection
related_species:
  taxonomic_rank: "genus"  # genus, family
  max_species_per_genus: 20
  min_genomes_per_species: 1
  max_genomes_per_species: 10

# Stage 3: Quality Assessment & Filtering
quality_assessment:
  # Genome size filters (in bp)
  min_genome_size: 1000000  # 1 Mb
  max_genome_size: 20000000  # 20 Mb
  genome_size_outlier_threshold: 3.0  # standard deviations
  
  # GC content filters (percentage)
  gc_content_outlier_threshold: 3.0  # standard deviations
  
  # Assembly quality metrics
  min_n50: 10000  # 10 kb
  max_contigs: 1000
  max_n_content: 0.05  # 5% N content
  
  # Completeness/contamination (if available)
  min_completeness: 90.0  # percentage
  max_contamination: 5.0  # percentage

# Stage 4: Taxonomic Validation via ANI
ani_analysis:
  ani_method: "skani"  # skani, fastani, pyani
  same_species_threshold: 95.0  # ANI percentage
  related_species_threshold: 85.0  # ANI percentage
  # FastANI specific parameters
  fragment_length: 3000
  min_fraction: 0.2
  # skani specific parameters
  skani_preset: "medium"  # fast, medium, slow
  skani_min_af: 0.15  # minimum alignment fraction
  clustering_method: "average"  # single, complete, average, ward

# Stage 5: Species-Specific K-mer Analysis
kmer_analysis:
  kmer_size: 21
  min_kmer_frequency: 5  # minimum occurrences in target species
  max_kmer_frequency_related: 0  # maximum occurrences in related species
  specificity_threshold: 0.95  # minimum specificity score
  jellyfish_hash_size: "1G"
  canonical_kmers: true

# Stage 6: Genomic Region Identification
region_identification:
  window_size: 1000  # bp
  step_size: 500  # bp
  min_specific_kmers_per_window: 10
  min_conservation_across_genomes: 0.8  # fraction of target genomes
  region_extension: 200  # bp to extend regions for primer design
  max_regions_per_genome: 100

# Stage 7: Primer Design
primer_design:
  # Primer3 settings
  primer_opt_size: 20
  primer_min_size: 18
  primer_max_size: 25
  primer_opt_tm: 60.0
  primer_min_tm: 57.0
  primer_max_tm: 63.0
  primer_opt_gc_percent: 50.0
  primer_min_gc: 40.0
  primer_max_gc: 60.0
  primer_max_poly_x: 4
  primer_gc_clamp: 1
  
  # PCR product settings
  product_size_range: [100, 300]  # bp
  
  # Specificity validation
  max_mismatches: 2
  max_primer_hits_related: 0  # maximum hits in related species
  
  # Output settings
  max_primer_pairs: 10
  rank_by: "specificity"  # specificity, tm_difference, gc_content

# Checkpoint settings
checkpoints:
  save_intermediate: true
  compress_checkpoints: true
  cleanup_temp_files: false
