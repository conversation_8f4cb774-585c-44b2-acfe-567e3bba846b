# Species-Specific Primer Design Pipeline

A modular microbial species-specific primer design pipeline that takes only a species Latin name as input and outputs PCR detection primers specific to that species.

## Features

- **Modular Design**: Each stage is implemented as a separate module with clear interfaces
- **Checkpoints & Resumability**: Pipeline can be resumed from any stage
- **Quality Control**: Comprehensive genome quality assessment and filtering
- **Taxonomic Validation**: ANI-based validation to identify misclassified genomes
- **Species Specificity**: K-mer analysis to identify species-specific genomic regions
- **Automated Primer Design**: PCR primer design with specificity validation

## Pipeline Stages

1. **Genome Download & Selection** - Download and select high-quality genomes
2. **Related Species Collection** - Collect genomes from related species for comparison
3. **Quality Assessment & Filtering** - Evaluate and filter genomes by quality metrics
4. **Taxonomic Validation via ANI** - Validate taxonomic classifications using ANI
5. **Species-Specific K-mer Analysis** - Identify species-specific k-mers
6. **Genomic Region Identification** - Map k-mers to genomic regions
7. **Primer Design** - Design and validate PCR primers

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd ss_primer_design

# Install dependencies
pip install -r requirements.txt

# Install NCBI datasets tool
conda install -c conda-forge ncbi-datasets-cli
```

## Usage

### Basic Usage
```bash
python primer_pipeline.py --species "Escherichia coli"
```

### Advanced Usage
```bash
# Resume from a specific stage
python primer_pipeline.py --species "Escherichia coli" --start-stage 3

# Use custom configuration
python primer_pipeline.py --species "Escherichia coli" --config custom_config.yaml

# Specify output directory
python primer_pipeline.py --species "Escherichia coli" --output-dir /path/to/output

# Auto-resume from last checkpoint
python primer_pipeline.py --species "Escherichia coli" --resume
```

## Configuration

The pipeline uses YAML configuration files to specify parameters for each stage. See `config/default_config.yaml` for all available options.

## Output

The pipeline generates:
- Species-specific primer pairs with validation metrics
- Quality assessment reports
- ANI analysis results
- K-mer analysis summaries
- Checkpoint files for resumability

## Requirements

- Python 3.8+
- NCBI datasets CLI tool
- Various bioinformatics Python packages (see requirements.txt)

## License

MIT License
